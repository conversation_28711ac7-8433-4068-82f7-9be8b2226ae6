#!/usr/bin/env python3
"""
Quick backtest test with optimizations
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def quick_test():
    """Quick test without heavy imports"""
    try:
        # Test strategy loading
        import json
        from pathlib import Path
        
        strategy_file = Path("data/strategies/optimized_strategies.json")
        if strategy_file.exists():
            with open(strategy_file) as f:
                strategies = json.load(f)
            print(f"✓ Loaded {len(strategies)} strategies")
            
            # Check data directories
            data_dirs = [
                "data/historical/5min",
                "data/features/5min"
            ]
            
            for dir_path in data_dirs:
                path = Path(dir_path)
                if path.exists():
                    files = list(path.glob("*.parquet"))
                    print(f"✓ Found {len(files)} files in {dir_path}")
                else:
                    print(f"⚠ Directory not found: {dir_path}")
            
            print("\nOptimizations applied:")
            print("- Limited to 5min timeframe only")
            print("- Process only first 3 days")
            print("- Sample every 10th data point")
            print("- Max 20 option files per type")
            print("- Max 3 failed attempts per strategy")
            
            return True
        else:
            print("✗ Strategy file not found")
            return False
            
    except Exception as e:
        print(f"✗ Error: {e}")
        return False

if __name__ == "__main__":
    success = quick_test()
    print(f"\nQuick test {'PASSED' if success else 'FAILED'}")
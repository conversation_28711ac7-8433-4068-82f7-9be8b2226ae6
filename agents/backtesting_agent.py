#!/usr/bin/env python3
"""
High-Performance Options Backtesting Agent - Modular and Maintainable
"""
import asyncio
import logging
import multiprocessing as mp
import polars as pl
import json
import aiofiles
import re # Added for regex matching in filenames
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

from .backtesting.config import BacktestConfig
from .backtesting.data_loader import load_feature_engineered_data, load_strategies
from .backtesting.engine import HighPerformanceBacktestEngine
from .backtesting.strategy_definition_backtester import StrategyDefinitionBacktester
from .backtesting.indian_options_data_loader import IndianOptionsDataLoader
from .backtesting.reporting import (
    generate_backtest_report,
    save_results_for_ai_training,
)
from .strategy_generation.data_models import StrategyDefinition

logger = logging.getLogger(__name__)


class OptionsBacktestingAgent:
    """
    Orchestrates the options backtesting process.
    """

    def __init__(self, config_path: str = "config/options_backtesting_config.yaml"):
        self.config_path = config_path
        self.config: BacktestConfig = None
        self.engine: HighPerformanceBacktestEngine = None
        self.strategy_definition_backtester: StrategyDefinitionBacktester = None
        self.indian_data_loader: IndianOptionsDataLoader = None
        self.backtest_results: Dict = {}
        self.strategy_definitions: List[StrategyDefinition] = []

    async def initialize(self, **kwargs):
        """Initialize the agent."""
        try:
            await self._load_config()
            self.engine = HighPerformanceBacktestEngine(risk_free_rate=0.06)
            self.strategy_definition_backtester = StrategyDefinitionBacktester()
            self.indian_data_loader = IndianOptionsDataLoader()
            await self._load_strategy_definitions()
            logger.info("Options Backtesting Agent initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize agent: {e}")
            return False

    async def _load_config(self):
        """Load configuration."""
        # Using default config as per instructions
        self.config = BacktestConfig(
            start_date="2025-08-01",
            end_date="2025-08-31",
            initial_capital=100000.0,
        )
        logger.info("Configuration loaded successfully")

    async def start(self, **kwargs) -> bool:
        """Start the backtesting process."""
        try:
            logger.info("Starting Options Backtesting Agent...")

            # Get date parameters
            start_date = kwargs.get('start_date') or self.config.start_date
            end_date = kwargs.get('end_date') or self.config.end_date
            
            # Check if we should use strategy definitions or traditional strategies
            use_strategy_definitions = kwargs.get('use_strategy_definitions', True)

            if use_strategy_definitions and self.strategy_definitions:
                logger.info("Using strategy definitions for backtesting")
                success = await self.backtest_strategy_definitions(start_date, end_date)
                if success:
                    logger.info("Strategy definition backtesting completed successfully")
                    return True
                else:
                    logger.warning("Strategy definition backtesting failed, falling back to traditional strategies")

            # Traditional strategy backtesting (fallback or primary if no strategy definitions)
            strategies = await load_strategies()
            if not strategies:
                logger.warning("No strategies found for backtesting")
                return False

            # Load integrated Indian options data
            historical_data, option_chains = await self.indian_data_loader.load_integrated_backtest_data(
                start_date, end_date
            )
            
            if historical_data.is_empty():
                logger.warning("No historical data found for the specified date range")
                return False

            self.backtest_results = self.engine.run_backtest(
                historical_data, strategies, self.config
            )

            await generate_backtest_report(self.backtest_results)
            await save_results_for_ai_training(self.backtest_results)

            logger.info("Traditional backtesting completed successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to start agent: {e}", exc_info=True)
            return False

    async def _load_strategy_definitions(self):
        """Load strategy definitions from file"""
        try:
            import json
            import aiofiles
            from pathlib import Path

            # Prioritize loading the main strategy_definitions.json file
            strategy_def_path = Path("data/strategies")
            # Try comprehensive strategies first, then optimized
            strategy_files = [
                "comprehensive_strategies.json",
                "optimized_strategies.json"
            ]

            latest_file = None
            for strategy_file in strategy_files:
                candidate_file = strategy_def_path / strategy_file
                if candidate_file.exists():
                    latest_file = candidate_file
                    logger.info(f"Loading strategy definition file: {strategy_file}")
                    break

            if latest_file:
                pass  # latest_file is already set
            else:
                # Fallback to looking for timestamped files if none of the main ones exist
                definition_files = list(strategy_def_path.glob("strategy_definitions_*.json"))
                if not definition_files:
                    logger.warning("No strategy definition files found")
                    return
                latest_file = max(definition_files, key=lambda x: x.stat().st_mtime)
                logger.info(f"Loading most recent timestamped strategy definition file: {latest_file.name}")

            async with aiofiles.open(latest_file, 'r') as f:
                content = await f.read()
                definitions_data = json.loads(content)

            # Convert to StrategyDefinition objects with proper deserialization
            self.strategy_definitions = []
            for def_data in definitions_data:
                try:
                    # Properly deserialize selection criteria
                    selection_criteria = {}
                    for leg_name, criteria_data in def_data['selection_criteria'].items():
                        from agents.strategy_generation.data_models import OptionSelectionCriteria
                        criteria = OptionSelectionCriteria(
                            moneyness=criteria_data['moneyness'],
                            dte_range=criteria_data['dte_range'],
                            delta_range=criteria_data.get('delta_range'),
                            strike_range=criteria_data.get('strike_range'),
                            volume_threshold=criteria_data.get('volume_threshold'),
                            open_interest_threshold=criteria_data.get('open_interest_threshold'),
                            max_bid_ask_spread=criteria_data.get('max_bid_ask_spread')
                        )
                        selection_criteria[leg_name] = criteria

                    # Create StrategyDefinition with proper types
                    from agents.strategy_generation.data_models import StrategyType
                    strategy_type = StrategyType(def_data['strategy_type']) if isinstance(def_data['strategy_type'], str) else def_data['strategy_type']

                    strategy_def = StrategyDefinition(
                        strategy_id=def_data['strategy_id'],
                        strategy_type=strategy_type,
                        underlying=def_data['underlying'],
                        name=def_data['name'],
                        description=def_data['description'],
                        selection_criteria=selection_criteria,
                        entry_conditions=def_data['entry_conditions'],
                        exit_conditions=def_data['exit_conditions'],
                        risk_management=def_data['risk_management'],
                        market_outlook=def_data['market_outlook'],
                        volatility_outlook=def_data['volatility_outlook'],
                        timeframe=def_data['timeframe'],
                        created_at=datetime.fromisoformat(def_data['created_at']),
                        tags=def_data['tags']
                    )
                    self.strategy_definitions.append(strategy_def)
                    logger.debug(f"Loaded strategy definition: {strategy_def.strategy_id}")
                except Exception as e:
                    logger.error(f"Failed to load strategy definition {def_data.get('strategy_id', 'unknown')}: {e}")
                    import traceback
                    logger.debug(f"Error details: {traceback.format_exc()}")

            logger.info(f"Loaded {len(self.strategy_definitions)} strategy definitions from {latest_file.name}")

        except Exception as e:
            logger.error(f"Failed to load strategy definitions: {e}")
            self.strategy_definitions = []

    async def backtest_strategy_definitions(self, start_date: str, end_date: str) -> bool:
        """Backtest strategy definitions using Indian options data"""
        try:
            if not self.strategy_definitions:
                logger.warning("No strategy definitions available for backtesting")
                return False
            
            # Load integrated data for strategy definition backtesting
            historical_data, option_chains = await self.indian_data_loader.load_integrated_backtest_data(
                start_date, end_date
            )
            
            if historical_data.is_empty():
                logger.warning("No historical data available for strategy definition backtesting")
                return False
            
            if not option_chains:
                logger.warning("No option chain data available for strategy definition backtesting")
                return False
            
            # Run strategy definition backtesting
            results = await self.strategy_definition_backtester.backtest_strategy_definitions(
                self.strategy_definitions, historical_data, option_chains, start_date, end_date
            )
            
            if results:
                self.backtest_results.update(results)
                logger.info(f"Successfully backtested {len(results)} strategy definitions")
                
                # Generate reports
                await generate_backtest_report(results)
                await save_results_for_ai_training(results)
                
                return True
            else:
                logger.warning("No results from strategy definition backtesting")
                return False
                
        except Exception as e:
            logger.error(f"Failed to backtest strategy definitions: {e}")
            return False

    async def _legacy_backtest_strategy_definitions(self, start_date: str = None, end_date: str = None) -> bool:
        """Legacy backtest strategy definitions using historical data"""
        try:
            if not self.strategy_definitions:
                logger.warning("No strategy definitions to backtest")
                return False

            logger.info(f"Starting backtest for {len(self.strategy_definitions)} strategy definitions")

            # Load historical data
            feature_data = await load_feature_engineered_data(
                start_date or self.config.start_date,
                end_date or self.config.end_date
            )

            if feature_data is None or feature_data.is_empty():
                logger.warning("No historical data available, creating synthetic data for testing")
                feature_data = await self._create_synthetic_market_data_for_backtest(
                    start_date or self.config.start_date,
                    end_date or self.config.end_date
                )

            # Load historical option chains (simplified - in practice you'd load real historical option data)
            historical_option_chains = await self._load_historical_option_chains(
                start_date or self.config.start_date,
                end_date or self.config.end_date
            )

            # Run backtests using strategy definitions
            self.backtest_results = await self.strategy_definition_backtester.backtest_strategy_definitions(
                self.strategy_definitions,
                feature_data,
                historical_option_chains,
                start_date or self.config.start_date,
                end_date or self.config.end_date
            )

            if self.backtest_results:
                logger.info(f"Completed backtesting for {len(self.backtest_results)} strategy definitions")

                # Generate enhanced reports
                await self._generate_strategy_definition_reports()
                await generate_backtest_report(self.backtest_results)
                await save_results_for_ai_training(self.backtest_results)

                return True
            else:
                logger.warning("No backtest results generated")
                return False

        except Exception as e:
            logger.error(f"Failed to backtest strategy definitions: {e}")
            return False

    async def _load_historical_option_chains(self, start_date: str, end_date: str) -> Dict[str, pl.DataFrame]:
        """Load historical option chain data with enhanced logic"""
        try:
            option_chains = {}

            # Try multiple data sources in order of preference
            # Try multiple data sources in order of preference, including recursive search
            data_sources = [
                Path("data/historical"), # Search recursively within historical
                Path("data/live"),       # Search recursively within live
                Path("data/options"),
                Path("data")
            ]

            found_data_path = None
            for data_path in data_sources:
                if data_path.exists():
                    logger.info(f"Looking for option chain data in {data_path}")
                    found_data_path = data_path
                    break
            else:
                logger.warning("No option chain data directory found, creating synthetic data")
                return await self._create_synthetic_option_chains(start_date, end_date)

            # Look for option chain files for each underlying
            underlyings = ['NIFTY', 'BANKNIFTY']

            for underlying in underlyings:
                all_underlying_chains = []
                # Search for actual option chain files (not Index files)
                # Index files contain underlying price data, not option chain data
                file_patterns = [
                    f"**/{underlying}_*_CE_*.parquet",    # Call options
                    f"**/{underlying}_*_PE_*.parquet",    # Put options
                    f"**/{underlying}_options_*.parquet", # General option files
                    f"**/{underlying}_*_CE_*.csv",        # Call options CSV
                    f"**/{underlying}_*_PE_*.csv",        # Put options CSV
                    f"**/{underlying}_options_*.csv"      # General option files CSV
                ]

                # Skip Index files as they don't contain option chain data
                excluded_patterns = [
                    f"**/*Index_{underlying}_*.parquet",
                    f"**/*Index_{underlying}_*.csv"
                ]

                for pattern in file_patterns:
                    # Use found_data_path for globbing
                    files = list(found_data_path.glob(pattern))
                    if files:
                        logger.info(f"Found {len(files)} option chain files for {underlying} with pattern {pattern} in {found_data_path}")
                        for file_path in files:
                            # Skip Index files explicitly
                            if "Index_" in file_path.name:
                                logger.debug(f"Skipping Index file {file_path.name} - contains underlying data, not option chain data")
                                continue
                            try:
                                if file_path.suffix == '.parquet':
                                    df = pl.read_parquet(file_path)
                                elif file_path.suffix == '.csv':
                                    df = pl.read_csv(file_path)
                                else:
                                    logger.warning(f"Unsupported file type for {file_path.name}")
                                    continue

                                if df is not None and df.height > 0:
                                    # Log available columns for debugging
                                    logger.debug(f"Columns in {file_path.name}: {df.columns}")
                                    # Ensure 'date' column exists and is in datetime format for filtering
                                    if 'date' in df.columns:
                                        df = df.with_columns(pl.col('date').str.strptime(pl.Date, fmt="%Y-%m-%d", strict=False).alias('date'))
                                    else:
                                        # If no date column, try to infer from filename or add current date
                                        try:
                                            date_str_match = re.search(r'(\d{8})', file_path.name)
                                            if date_str_match:
                                                file_date = datetime.strptime(date_str_match.group(1), '%Y%m%d').date()
                                                df = df.with_columns(pl.lit(file_date).alias('date'))
                                            else:
                                                df = df.with_columns(pl.lit(datetime.now().date()).alias('date'))
                                        except Exception:
                                            df = df.with_columns(pl.lit(datetime.now().date()).alias('date'))

                                    logger.debug(f"DataFrame schema after date parsing: {df.schema}")
                                    logger.debug(f"First 5 rows of 'date' column: {df.select('date').head(5)}")

                                    # Filter by date range
                                    start_dt_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                                    end_dt_obj = datetime.strptime(end_date, '%Y-%m-%d').date()

                                    df_filtered = df.filter(
                                        (pl.col('date') >= pl.lit(start_dt_obj, pl.Date)) &
                                        (pl.col('date') <= pl.lit(end_dt_obj, pl.Date))
                                    )
                                    logger.debug(f"DataFrame height after date filtering: {df_filtered.height}")

                                    if df_filtered.height > 0:
                                        # Standardize columns before appending to ensure consistent schema
                                        df_standardized = await self._standardize_option_chain_columns(df_filtered, underlying)
                                        logger.info(f"Loaded and filtered historical option chain for {underlying} from {file_path.name} ({df_standardized.height} rows)")
                                        all_underlying_chains.append(df_standardized)
                                    else:
                                        logger.info(f"No data for {underlying} in {file_path.name} within the specified date range.")
                                else:
                                    logger.warning(f"File {file_path.name} is empty or could not be loaded.")
                            except Exception as e:
                                logger.warning(f"Failed to load or process {file_path}: {e}", exc_info=True)
                                continue

                if all_underlying_chains:
                    # Concatenate all standardized dataframes
                    option_chain = pl.concat(all_underlying_chains).unique(subset=['symbol', 'expiry_date', 'strike_price', 'option_type', 'date'])
                    option_chains[underlying] = option_chain
                else:
                    logger.warning(f"No historical option chain data found for {underlying} within the specified date range, creating synthetic data")
                    synthetic_chain = await self._create_synthetic_option_chain(underlying, start_date, end_date)
                    if synthetic_chain is not None:
                        option_chains[underlying] = synthetic_chain

            logger.info(f"Loaded option chains for {len(option_chains)} underlyings")
            return option_chains

        except Exception as e:
            logger.error(f"Failed to load historical option chains: {e}")
            # Fallback to synthetic data
            return await self._create_synthetic_option_chains(start_date, end_date)

    async def _standardize_option_chain_columns(self, option_chain: pl.DataFrame, underlying: str) -> pl.DataFrame:
        """Standardize option chain column names and add missing columns"""
        try:
            # Check if DataFrame is empty
            if option_chain.height == 0:
                logger.warning(f"Empty option chain DataFrame for {underlying}")
                return self._create_empty_option_chain_schema()

            # Map common column name variations
            column_mapping = {
                'Symbol': 'symbol',
                'SYMBOL': 'symbol',
                'Strike': 'strike_price',
                'STRIKE': 'strike_price',
                'strike': 'strike_price',
                'StrikePrice': 'strike_price',
                'LTP': 'ltp',
                'ltp': 'ltp',
                'LastPrice': 'ltp',
                'last_price': 'ltp',
                'Volume': 'volume',
                'VOLUME': 'volume',
                'vol': 'volume',
                'OpenInterest': 'open_interest',
                'OPEN_INTEREST': 'open_interest',
                'oi': 'open_interest',
                'Bid': 'bid',
                'BID': 'bid',
                'Ask': 'ask',
                'ASK': 'ask',
                'Delta': 'delta',
                'DELTA': 'delta',
                'Expiry': 'expiry_date',
                'EXPIRY': 'expiry_date',
                'expiry': 'expiry_date',
                'ExpiryDate': 'expiry_date',
                'OptionType': 'option_type',
                'OPTION_TYPE': 'option_type',
                'option_type': 'option_type',
                'Type': 'option_type'
            }

            # Rename columns
            for old_name, new_name in column_mapping.items():
                if old_name in option_chain.columns:
                    option_chain = option_chain.rename({old_name: new_name})

            # Check for critical missing columns and log them
            critical_columns = ['strike_price', 'option_type']
            missing_critical = [col for col in critical_columns if col not in option_chain.columns]
            if missing_critical:
                logger.warning(f"Missing critical columns for {underlying}: {missing_critical}")
                # Try to infer from other columns or filename
                option_chain = self._infer_missing_columns(option_chain, underlying)

            # Define the final set of desired columns and their types
            final_schema = {
                'symbol': pl.Utf8,
                'strike_price': pl.Float64,
                'ltp': pl.Float64,
                'volume': pl.Int64,
                'open_interest': pl.Int64,
                'option_type': pl.Utf8,
                'expiry_date': pl.Utf8, # Keep as Utf8 for now, conversion to Date can happen later if needed
                'bid': pl.Float64,
                'ask': pl.Float64,
                'delta': pl.Float64,
                'date': pl.Date
            }

            # Create a list of expressions to select and cast columns to the final schema
            expressions = []
            for col_name, col_type in final_schema.items():
                if col_name in option_chain.columns:
                    expressions.append(pl.col(col_name).cast(col_type, strict=False))
                else:
                    # Add missing columns with appropriate default values and cast to target type
                    if col_name == 'symbol':
                        # Ensure strike_price and option_type are available before attempting to build symbol
                        if 'strike_price' in option_chain.columns and 'option_type' in option_chain.columns:
                            expressions.append(
                                pl.when(pl.col('strike_price').is_not_null() & pl.col('option_type').is_not_null())
                                .then(pl.lit(underlying) + pl.col('strike_price').cast(pl.Utf8) + pl.col('option_type'))
                                .otherwise(pl.lit(f"{underlying}_OPTION")).alias('symbol').cast(col_type)
                            )
                        else:
                            expressions.append(pl.lit(f"{underlying}_OPTION").alias('symbol').cast(col_type))
                    elif col_name == 'volume':
                        expressions.append(pl.lit(1000).alias('volume').cast(col_type))
                    elif col_name == 'open_interest':
                        expressions.append(pl.lit(500).alias('open_interest').cast(col_type))
                    elif col_name == 'ltp':
                        expressions.append(pl.lit(100.0).alias('ltp').cast(col_type))
                    elif col_name == 'option_type':
                        # Try to infer from symbol if available, otherwise default
                        if 'symbol' in option_chain.columns:
                            expressions.append(
                                pl.when(pl.col('symbol').str.contains('PE')).then(pl.lit('PE'))
                                .when(pl.col('symbol').str.contains('CE')).then(pl.lit('CE'))
                                .otherwise(pl.lit('CE')).alias('option_type').cast(col_type)
                            )
                        else:
                            expressions.append(pl.lit('CE').alias('option_type').cast(col_type))
                    elif col_name == 'date':
                        expressions.append(pl.lit(datetime.now().strftime('%Y-%m-%d')).alias('date').cast(col_type))
                    else:
                        expressions.append(pl.lit(None).alias(col_name).cast(col_type))

            # Select and cast all columns to the final schema
            option_chain = option_chain.select(expressions)


            logger.info(f"Standardized option chain for {underlying}: {option_chain.height} rows, {len(option_chain.columns)} columns")
            return option_chain

        except Exception as e:
            logger.error(f"Failed to standardize option chain columns for {underlying}: {e}")
            logger.error(f"Available columns: {option_chain.columns if 'option_chain' in locals() else 'N/A'}")
            # Return an empty DataFrame with the expected schema on error
            return self._create_empty_option_chain_schema()

    def _create_empty_option_chain_schema(self) -> pl.DataFrame:
        """Create an empty DataFrame with the expected option chain schema"""
        empty_df_schema = {
            'symbol': pl.Utf8,
            'strike_price': pl.Float64,
            'ltp': pl.Float64,
            'volume': pl.Int64,
            'open_interest': pl.Int64,
            'option_type': pl.Utf8,
            'expiry_date': pl.Utf8,
            'bid': pl.Float64,
            'ask': pl.Float64,
            'delta': pl.Float64,
            'date': pl.Date
        }
        return pl.DataFrame({}, schema=empty_df_schema)

    def _infer_missing_columns(self, option_chain: pl.DataFrame, underlying: str) -> pl.DataFrame:
        """Try to infer missing critical columns from available data"""
        try:
            # Try to infer strike_price from symbol if missing
            if 'strike_price' not in option_chain.columns and 'symbol' in option_chain.columns:
                # Extract strike price from symbol (e.g., NIFTY23800CE -> 23800)
                option_chain = option_chain.with_columns([
                    pl.col('symbol').str.extract(r'(\d+)(?:CE|PE)', 1).cast(pl.Float64, strict=False).alias('strike_price')
                ])
                logger.info(f"Inferred strike_price from symbol for {underlying}")

            # Try to infer option_type from symbol if missing
            if 'option_type' not in option_chain.columns and 'symbol' in option_chain.columns:
                option_chain = option_chain.with_columns([
                    pl.when(pl.col('symbol').str.contains('CE')).then(pl.lit('CE'))
                    .when(pl.col('symbol').str.contains('PE')).then(pl.lit('PE'))
                    .otherwise(pl.lit('CE')).alias('option_type')
                ])
                logger.info(f"Inferred option_type from symbol for {underlying}")

            return option_chain

        except Exception as e:
            logger.error(f"Failed to infer missing columns for {underlying}: {e}")
            return option_chain

    async def _create_synthetic_option_chains(self, start_date: str, end_date: str) -> Dict[str, pl.DataFrame]:
        """Create synthetic option chain data for backtesting"""
        try:
            option_chains = {}

            for underlying in ['NIFTY', 'BANKNIFTY']:
                synthetic_chain = await self._create_synthetic_option_chain(underlying, start_date, end_date)
                if synthetic_chain is not None:
                    option_chains[underlying] = synthetic_chain

            return option_chains

        except Exception as e:
            logger.error(f"Failed to create synthetic option chains: {e}")
            return {}

    async def _create_synthetic_option_chain(self, underlying: str, start_date: str, end_date: str) -> Optional[pl.DataFrame]:
        """Create synthetic option chain for a specific underlying"""
        try:
            import numpy as np
            from datetime import datetime, timedelta

            # Base prices for different underlyings
            base_prices = {
                'NIFTY': 24000,
                'BANKNIFTY': 52000
            }

            base_price = base_prices.get(underlying, 24000)

            # Generate strikes around the base price
            strike_range = np.arange(
                base_price * 0.8,  # 20% below
                base_price * 1.2,  # 20% above
                50 if underlying == 'NIFTY' else 100  # Strike intervals
            )

            # Generate expiry dates
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')

            # Weekly expiries (Thursdays)
            expiry_dates = []
            current_date = start_dt
            while current_date <= end_dt:
                # Find next Thursday
                days_ahead = 3 - current_date.weekday()  # Thursday is 3
                if days_ahead <= 0:
                    days_ahead += 7
                expiry_date = current_date + timedelta(days=days_ahead)
                if expiry_date <= end_dt + timedelta(days=30):  # Include some future expiries
                    expiry_dates.append(expiry_date.strftime('%Y-%m-%d'))
                current_date += timedelta(days=7)

            # Create option chain data
            option_data = []

            for expiry in expiry_dates:
                for strike in strike_range:
                    for option_type in ['CE', 'PE']:
                        # Calculate synthetic Greeks and prices
                        moneyness = strike / base_price

                        if option_type == 'CE':
                            # Call option
                            if moneyness < 0.95:  # ITM
                                ltp = base_price - strike + np.random.normal(50, 20)
                                delta = 0.7 + np.random.normal(0, 0.1)
                            elif moneyness > 1.05:  # OTM
                                ltp = max(5, np.random.normal(30, 15))
                                delta = 0.2 + np.random.normal(0, 0.1)
                            else:  # ATM
                                ltp = np.random.normal(100, 30)
                                delta = 0.5 + np.random.normal(0, 0.1)
                        else:
                            # Put option
                            if moneyness > 1.05:  # ITM
                                ltp = strike - base_price + np.random.normal(50, 20)
                                delta = -0.7 + np.random.normal(0, 0.1)
                            elif moneyness < 0.95:  # OTM
                                ltp = max(5, np.random.normal(30, 15))
                                delta = -0.2 + np.random.normal(0, 0.1)
                            else:  # ATM
                                ltp = np.random.normal(100, 30)
                                delta = -0.5 + np.random.normal(0, 0.1)

                        ltp = max(1, ltp)  # Minimum price
                        delta = max(-1, min(1, delta))  # Clamp delta

                        # Generate volume and OI based on moneyness
                        if 0.95 <= moneyness <= 1.05:  # ATM
                            volume = np.random.randint(5000, 20000)
                            oi = np.random.randint(10000, 50000)
                        elif 0.9 <= moneyness <= 1.1:  # Near ATM
                            volume = np.random.randint(1000, 10000)
                            oi = np.random.randint(5000, 25000)
                        else:  # Far OTM/ITM
                            volume = np.random.randint(100, 2000)
                            oi = np.random.randint(500, 5000)

                        symbol = f"{underlying}{expiry.replace('-', '')}{int(strike)}{option_type}"

                        option_data.append({
                            'symbol': symbol,
                            'underlying': underlying,
                            'strike_price': strike,
                            'option_type': option_type,
                            'expiry_date': expiry,
                            'ltp': round(ltp, 2),
                            'bid': round(ltp * 0.98, 2),
                            'ask': round(ltp * 1.02, 2),
                            'volume': volume,
                            'open_interest': oi,
                            'delta': round(delta, 3),
                            'date': start_date
                        })

            if option_data:
                synthetic_chain = pl.DataFrame(option_data)
                logger.info(f"Created synthetic option chain for {underlying}: {len(option_data)} options")
                return synthetic_chain
            else:
                logger.warning(f"No synthetic option data created for {underlying}")
                return None

        except Exception as e:
            logger.error(f"Failed to create synthetic option chain for {underlying}: {e}")
            return None

    async def _generate_strategy_definition_reports(self):
        """Generate enhanced reports for strategy definition backtesting"""
        try:
            if not self.backtest_results:
                return

            # Create summary report
            summary_data = []
            for strategy_id, results in self.backtest_results.items():
                summary_data.append({
                    'strategy_id': strategy_id,
                    'total_trades': results.total_trades,
                    'win_rate': results.win_rate,
                    'total_return': results.total_return,
                    'sharpe_ratio': results.sharpe_ratio,
                    'max_drawdown': results.max_drawdown,
                    'profit_factor': results.profit_factor
                })

            # Save summary to file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            summary_file = Path("data/backtesting") / f"strategy_definition_summary_{timestamp}.json"
            summary_file.parent.mkdir(parents=True, exist_ok=True)

            async with aiofiles.open(summary_file, 'w') as f:
                await f.write(json.dumps(summary_data, indent=2, default=str))

            # Log summary statistics
            if summary_data:
                avg_win_rate = sum(s['win_rate'] for s in summary_data) / len(summary_data)
                avg_return = sum(s['total_return'] for s in summary_data) / len(summary_data)
                avg_sharpe = sum(s['sharpe_ratio'] for s in summary_data) / len(summary_data)

                logger.info("=== STRATEGY DEFINITION BACKTEST SUMMARY ===")
                logger.info(f"Total Strategies Tested: {len(summary_data)}")
                logger.info(f"Average Win Rate: {avg_win_rate:.2%}")
                logger.info(f"Average Return: {avg_return:.2%}")
                logger.info(f"Average Sharpe Ratio: {avg_sharpe:.2f}")

                # Find best performing strategies
                best_return = max(summary_data, key=lambda x: x['total_return'])
                best_sharpe = max(summary_data, key=lambda x: x['sharpe_ratio'])
                best_win_rate = max(summary_data, key=lambda x: x['win_rate'])

                logger.info(f"Best Return: {best_return['strategy_id']} ({best_return['total_return']:.2%})")
                logger.info(f"Best Sharpe: {best_sharpe['strategy_id']} ({best_sharpe['sharpe_ratio']:.2f})")
                logger.info(f"Best Win Rate: {best_win_rate['strategy_id']} ({best_win_rate['win_rate']:.2%})")
                logger.info("=" * 50)

            logger.info(f"Strategy definition backtest summary saved to {summary_file}")

        except Exception as e:
            logger.error(f"Failed to generate strategy definition reports: {e}")

    async def run_comprehensive_backtest(self, start_date: str = None, end_date: str = None,
                                       save_results: bool = True) -> Dict[str, Any]:
        """
        Run a comprehensive backtest of all strategy definitions

        Args:
            start_date: Start date for backtesting (YYYY-MM-DD)
            end_date: End date for backtesting (YYYY-MM-DD)
            save_results: Whether to save results to files

        Returns:
            Dictionary containing backtest results and summary statistics
        """
        try:
            logger.info("Starting comprehensive strategy definition backtest...")

            # Use provided dates or defaults
            start_date = start_date or "2024-01-01"
            end_date = end_date or "2024-12-31"

            # Run the backtest
            success = await self.backtest_strategy_definitions(start_date, end_date)

            if not success:
                logger.error("Comprehensive backtest failed")
                return {}

            # Calculate summary statistics
            summary_stats = await self._calculate_summary_statistics()

            # Prepare comprehensive results
            comprehensive_results = {
                'backtest_period': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'strategy_results': self.backtest_results,
                'summary_statistics': summary_stats,
                'total_strategies': len(self.strategy_definitions),
                'successful_backtests': len(self.backtest_results)
            }

            if save_results:
                # Save comprehensive results
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                results_file = Path("data/backtesting") / f"comprehensive_backtest_{timestamp}.json"
                results_file.parent.mkdir(parents=True, exist_ok=True)

                async with aiofiles.open(results_file, 'w') as f:
                    await f.write(json.dumps(comprehensive_results, indent=2, default=str))

                logger.info(f"Comprehensive backtest results saved to {results_file}")

            return comprehensive_results

        except Exception as e:
            logger.error(f"Failed to run comprehensive backtest: {e}")
            return {}

    async def _calculate_summary_statistics(self) -> Dict[str, Any]:
        """Calculate summary statistics across all backtested strategies"""
        try:
            if not self.backtest_results:
                return {}

            # Collect all metrics
            win_rates = []
            returns = []
            sharpe_ratios = []
            max_drawdowns = []
            profit_factors = []
            total_trades = []

            for results in self.backtest_results.values():
                win_rates.append(results.win_rate)
                returns.append(results.total_return)
                sharpe_ratios.append(results.sharpe_ratio)
                max_drawdowns.append(results.max_drawdown)
                profit_factors.append(results.profit_factor)
                total_trades.append(results.total_trades)

            # Calculate statistics
            import numpy as np

            summary = {
                'win_rate': {
                    'mean': np.mean(win_rates),
                    'median': np.median(win_rates),
                    'std': np.std(win_rates),
                    'min': np.min(win_rates),
                    'max': np.max(win_rates)
                },
                'total_return': {
                    'mean': np.mean(returns),
                    'median': np.median(returns),
                    'std': np.std(returns),
                    'min': np.min(returns),
                    'max': np.max(returns)
                },
                'sharpe_ratio': {
                    'mean': np.mean(sharpe_ratios),
                    'median': np.median(sharpe_ratios),
                    'std': np.std(sharpe_ratios),
                    'min': np.min(sharpe_ratios),
                    'max': np.max(sharpe_ratios)
                },
                'max_drawdown': {
                    'mean': np.mean(max_drawdowns),
                    'median': np.median(max_drawdowns),
                    'std': np.std(max_drawdowns),
                    'min': np.min(max_drawdowns),
                    'max': np.max(max_drawdowns)
                },
                'total_trades': {
                    'mean': np.mean(total_trades),
                    'median': np.median(total_trades),
                    'total': np.sum(total_trades)
                }
            }

            return summary

        except Exception as e:
            logger.error(f"Failed to calculate summary statistics: {e}")
            return {}

    async def _create_synthetic_market_data_for_backtest(self, start_date: str, end_date: str) -> pl.DataFrame:
        """Create synthetic market data for backtesting when historical data is not available"""
        try:
            import numpy as np
            from datetime import datetime, timedelta

            logger.info(f"[SYNTHETIC] Creating synthetic market data for backtest from {start_date} to {end_date}")

            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')

            # Generate daily data
            data = []
            current_date = start_dt
            base_price = 24000  # Base NIFTY price

            while current_date <= end_dt:
                # Generate multiple intraday points
                for hour in range(9, 16):  # Market hours 9 AM to 3:30 PM
                    for minute in [15, 30, 45]:  # 15-minute intervals
                        timestamp = current_date.replace(hour=hour, minute=minute)

                        # Generate realistic price movement
                        days_from_start = (current_date - start_dt).days
                        trend = np.sin(days_from_start * 0.1) * 200  # Long-term trend
                        noise = np.random.normal(0, 50)  # Daily noise
                        price = base_price + trend + noise

                        # Generate technical indicators
                        rsi = 50 + np.random.normal(0, 15)
                        rsi = max(0, min(100, rsi))

                        volume = np.random.randint(100000, 1000000)

                        data.append({
                            'timestamp': timestamp.isoformat(),
                            'underlying': 'NIFTY',
                            'close': price,
                            'ltp': price,
                            'open': price + np.random.normal(0, 10),
                            'high': price + abs(np.random.normal(0, 20)),
                            'low': price - abs(np.random.normal(0, 20)),
                            'volume': volume,
                            'rsi_14': rsi,
                            'rsi': rsi,
                            'sma_20': price + np.random.normal(0, 30),
                            'avg_volume_10d': volume * np.random.uniform(0.8, 1.2),
                            'volatility': np.random.uniform(0.15, 0.35),
                            'timeframe': '15min'
                        })

                current_date += timedelta(days=1)

                # Skip weekends
                if current_date.weekday() >= 5:
                    current_date += timedelta(days=2)

            synthetic_df = pl.DataFrame(data)
            logger.info(f"[SYNTHETIC] Created {synthetic_df.height} rows of synthetic market data for backtesting")
            return synthetic_df

        except Exception as e:
            logger.error(f"[ERROR] Failed to create synthetic market data for backtest: {e}")
            # Return minimal DataFrame
            return pl.DataFrame({
                'timestamp': [datetime.now().isoformat()],
                'underlying': ['NIFTY'],
                'close': [24000.0],
                'rsi_14': [50.0],
                'volume': [100000],
                'timeframe': ['15min']
            })

    async def cleanup(self):
        """Cleanup resources."""
        logger.info("Cleaning up Options Backtesting Agent...")


async def main():
    """Example usage of Options Backtesting Agent"""
    agent = OptionsBacktestingAgent()
    try:
        await agent.initialize()
        await agent.start()
    except KeyboardInterrupt:
        logger.info("Agent interrupted by user")
    finally:
        await agent.cleanup()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())

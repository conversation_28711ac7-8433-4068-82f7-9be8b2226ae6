#!/usr/bin/env python3
"""
Option Selector - Selects appropriate options based on strategy criteria

This module handles option selection for Indian options backtesting:
1. Filters options based on strategy selection criteria
2. Calculates moneyness, DTE, and other metrics
3. Returns selected options for strategy execution
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import polars as pl
import numpy as np
from dataclasses import dataclass

# Numba JIT imports for performance optimization
try:
    from numba import jit, njit, prange
    NUMBA_AVAILABLE = True
    logging.info("Numba JIT compilation available for option selection")
except ImportError:
    NUMBA_AVAILABLE = False
    def jit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    def njit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    prange = range

logger = logging.getLogger(__name__)

@dataclass
class SelectedOption:
    """Represents a selected option for trading"""
    symbol: str
    underlying: str
    strike_price: float
    option_type: str  # 'CE' or 'PE'
    premium: float
    volume: int
    open_interest: int
    delta: Optional[float] = None
    gamma: Optional[float] = None
    theta: Optional[float] = None
    vega: Optional[float] = None
    iv: Optional[float] = None
    dte: Optional[int] = None
    moneyness: Optional[str] = None
    quantity: int = 1

class OptionSelector:
    """Selects options based on strategy criteria"""
    
    def __init__(self):
        self.logger = logger
    
    def __init__(self):
        self.logger = logger
        self.failed_strategies = {}  # Track failed strategies to reduce logging

    async def select_options_for_strategy(self, strategy_def, option_chain_data: pl.DataFrame,
                                        current_price: float) -> List[SelectedOption]:
        """
        Select options based on strategy definition criteria

        Args:
            strategy_def: Strategy definition with selection criteria
            option_chain_data: Available options data
            current_price: Current underlying price

        Returns:
            List of selected options
        """
        try:
            selected_options = []

            if not hasattr(strategy_def, 'selection_criteria'):
                logger.warning("Strategy definition has no selection criteria")
                return []

            selection_criteria = strategy_def.selection_criteria
            strategy_id = getattr(strategy_def, 'strategy_id', 'unknown')

            # Track failed attempts to reduce logging
            if strategy_id not in self.failed_strategies:
                self.failed_strategies[strategy_id] = 0

            # Process each leg of the strategy
            for leg_name, criteria in selection_criteria.items():
                logger.debug(f"Processing leg: {leg_name}")

                # Filter options based on criteria
                filtered_options = await self._filter_options_by_criteria(
                    option_chain_data, criteria, current_price, leg_name
                )

                if filtered_options.is_empty():
                    logger.debug(f"No options found for leg {leg_name} after filtering")
                    logger.debug(f"Criteria for {leg_name}: {criteria.__dict__ if hasattr(criteria, '__dict__') else criteria}")
                    continue

                # Select best option from filtered results
                best_option = await self._select_best_option(filtered_options, criteria, current_price)

                if best_option:
                    selected_options.append(best_option)
                    logger.debug(f"Selected option for {leg_name}: {best_option.symbol} (strike: {best_option.strike_price}, premium: {best_option.premium})")
                else:
                    logger.debug(f"No best option found for {leg_name} after filtering {filtered_options.height} options")

            if selected_options:
                logger.info(f"Selected {len(selected_options)} options for strategy {strategy_id}")
                for opt in selected_options:
                    logger.debug(f"  - {opt.symbol}: {opt.option_type} {opt.strike_price} @ {opt.premium}")
                # Reset failure counter on success
                self.failed_strategies[strategy_id] = 0
            else:
                self.failed_strategies[strategy_id] += 1
                # Drastically reduce logging to prevent timeout
                if self.failed_strategies[strategy_id] == 1:
                    logger.warning(f"No options selected for strategy {strategy_id} (first failure)")
                elif self.failed_strategies[strategy_id] == 5:
                    logger.warning(f"Strategy {strategy_id} has failed 5 times, reducing log verbosity")
                elif self.failed_strategies[strategy_id] % 100 == 0:
                    logger.debug(f"Strategy {strategy_id} has failed {self.failed_strategies[strategy_id]} times")

            return selected_options

        except Exception as e:
            logger.error(f"Failed to select options for strategy: {e}")
            return []
    
    async def _filter_options_by_criteria(self, option_chain: pl.DataFrame, criteria, 
                                        current_price: float, leg_name: str) -> pl.DataFrame:
        """Filter options based on selection criteria"""
        try:
            if option_chain.is_empty():
                logger.debug(f"Empty option chain provided for {leg_name}")
                return option_chain
            
            filtered_df = option_chain.clone()
            logger.debug(f"Starting with {filtered_df.height} options for {leg_name}")
            logger.debug(f"Available columns: {filtered_df.columns}")
            
            # Ensure required columns exist with fallbacks
            filtered_df = await self._ensure_required_columns(filtered_df)
            
            # Filter by option type based on leg name
            if 'call' in leg_name.lower() or leg_name.lower().endswith('_ce'):
                filtered_df = filtered_df.filter(pl.col('option_type') == 'CE')
                logger.debug(f"After CE filter: {filtered_df.height} options")
            elif 'put' in leg_name.lower() or leg_name.lower().endswith('_pe'):
                filtered_df = filtered_df.filter(pl.col('option_type') == 'PE')
                logger.debug(f"After PE filter: {filtered_df.height} options")
            
            if filtered_df.is_empty():
                logger.debug(f"No options after option type filter for {leg_name}")
                return filtered_df
            
            # Filter by moneyness
            if hasattr(criteria, 'moneyness') and criteria.moneyness:
                filtered_df = await self._filter_by_moneyness(filtered_df, criteria.moneyness, current_price)

            if filtered_df.is_empty():
                return filtered_df

            # Filter by DTE range (be more lenient)
            if hasattr(criteria, 'dte_range') and criteria.dte_range:
                filtered_df = await self._filter_by_dte(filtered_df, criteria.dte_range)

            # Filter by delta range (be more lenient)
            if hasattr(criteria, 'delta_range') and criteria.delta_range:
                filtered_df = await self._filter_by_delta(filtered_df, criteria.delta_range)

            # Filter by strike range
            if hasattr(criteria, 'strike_range') and criteria.strike_range:
                filtered_df = await self._filter_by_strike_range(filtered_df, criteria.strike_range, current_price)
            
            # Be more lenient with volume and OI thresholds for backtesting
            if hasattr(criteria, 'volume_threshold') and criteria.volume_threshold:
                # Reduce threshold by 90% for backtesting to allow more options
                adjusted_threshold = max(1, criteria.volume_threshold * 0.1)
                filtered_df = filtered_df.filter(pl.col('volume') >= adjusted_threshold)

            if hasattr(criteria, 'open_interest_threshold') and criteria.open_interest_threshold:
                # Reduce threshold by 90% for backtesting
                adjusted_threshold = max(1, criteria.open_interest_threshold * 0.1)
                filtered_df = filtered_df.filter(pl.col('open_interest') >= adjusted_threshold)
            
            # Filter by bid-ask spread (be lenient)
            if hasattr(criteria, 'max_bid_ask_spread') and criteria.max_bid_ask_spread:
                if 'bid_ask_spread' in filtered_df.columns:
                    filtered_df = filtered_df.filter(pl.col('bid_ask_spread') <= criteria.max_bid_ask_spread)
                else:
                    # Fallback: filter by premium > minimum threshold
                    min_premium = 0.5  # Lower minimum for backtesting
                    premium_col = 'premium' if 'premium' in filtered_df.columns else 'ltp'
                    if premium_col in filtered_df.columns:
                        filtered_df = filtered_df.filter(pl.col(premium_col) >= min_premium)
            
            # If no options remain, try with more relaxed criteria
            if filtered_df.is_empty():
                # Try again with very relaxed criteria
                relaxed_df = option_chain.clone()

                # Apply only essential filters
                if hasattr(criteria, 'moneyness') and criteria.moneyness:
                    relaxed_df = await self._filter_by_moneyness(relaxed_df, criteria.moneyness, current_price)

                # Very minimal volume/OI requirements
                if 'volume' in relaxed_df.columns:
                    relaxed_df = relaxed_df.filter(pl.col('volume') >= 1)
                if 'open_interest' in relaxed_df.columns:
                    relaxed_df = relaxed_df.filter(pl.col('open_interest') >= 1)

                if not relaxed_df.is_empty():
                    return relaxed_df

            return filtered_df
            
        except Exception as e:
            logger.error(f"Failed to filter options by criteria for {leg_name}: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return pl.DataFrame()
    
    async def _filter_by_moneyness(self, df: pl.DataFrame, moneyness: str, current_price: float) -> pl.DataFrame:
        """Filter options by moneyness"""
        try:
            if current_price <= 0:
                logger.warning(f"Invalid current price: {current_price}, using default")
                current_price = 24000.0  # Default NIFTY price
            
            if moneyness.upper() == 'ATM':
                # ATM: within 3% of current price (more lenient for backtesting)
                tolerance = 0.03
                df = df.filter(
                    (pl.col('strike_price') >= current_price * (1 - tolerance)) &
                    (pl.col('strike_price') <= current_price * (1 + tolerance))
                )
            elif moneyness.upper() == 'ITM':
                # ITM: calls below current price, puts above current price
                df = df.filter(
                    ((pl.col('option_type') == 'CE') & (pl.col('strike_price') < current_price)) |
                    ((pl.col('option_type') == 'PE') & (pl.col('strike_price') > current_price))
                )
            elif moneyness.upper() == 'OTM':
                # OTM: calls above current price, puts below current price
                df = df.filter(
                    ((pl.col('option_type') == 'CE') & (pl.col('strike_price') > current_price)) |
                    ((pl.col('option_type') == 'PE') & (pl.col('strike_price') < current_price))
                )
            elif moneyness.upper() == 'SLIGHTLY_OTM':
                # Slightly OTM: 1-8% away from current price (more lenient)
                df = df.filter(
                    ((pl.col('option_type') == 'CE') &
                     (pl.col('strike_price') > current_price * 1.01) &
                     (pl.col('strike_price') <= current_price * 1.08)) |
                    ((pl.col('option_type') == 'PE') &
                     (pl.col('strike_price') < current_price * 0.99) &
                     (pl.col('strike_price') >= current_price * 0.92))
                )
            else:
                logger.warning(f"Unknown moneyness type: {moneyness}, returning all options")
            return df
            
        except Exception as e:
            logger.error(f"Failed to filter by moneyness: {e}")
            return df
    
    async def _filter_by_dte(self, df: pl.DataFrame, dte_range: List[int]) -> pl.DataFrame:
        """Filter options by days to expiry"""
        try:
            if len(dte_range) != 2:
                logger.warning(f"Invalid DTE range: {dte_range}, expected [min, max]")
                return df
            
            min_dte, max_dte = dte_range
            
            # Calculate DTE if not available
            if 'dte' not in df.columns:
                df = await self._calculate_dte(df)
            
            if 'dte' in df.columns:
                # Be more lenient with DTE filtering for backtesting
                # Expand the range by 50% to allow more options
                expanded_min = max(1, int(min_dte * 0.5))
                expanded_max = int(max_dte * 1.5)
                
                df = df.filter(
                    pl.col('dte').is_not_null() &
                    (pl.col('dte') >= expanded_min) &
                    (pl.col('dte') <= expanded_max)
                )
            else:
                logger.warning("DTE column not available after calculation, skipping DTE filter")
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to filter by DTE: {e}")
            return df
    
    async def _filter_by_delta(self, df: pl.DataFrame, delta_range: List[float]) -> pl.DataFrame:
        """Filter options by delta range"""
        try:
            if len(delta_range) != 2:
                return df
            
            min_delta, max_delta = delta_range
            
            # If delta not available, estimate based on moneyness
            if 'delta' not in df.columns:
                df = await self._estimate_delta(df)
            
            if 'delta' in df.columns:
                # Handle the case where delta values might be None/null
                df = df.filter(
                    pl.col('delta').is_not_null() &
                    (pl.col('delta') >= min_delta) & 
                    (pl.col('delta') <= max_delta)
                )
            else:
                # If we still don't have delta, be more lenient and return all options
                logger.warning("Delta column not available, skipping delta filter")
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to filter by delta: {e}")
            return df
    
    async def _filter_by_strike_range(self, df: pl.DataFrame, strike_range: List[str], 
                                    current_price: float) -> pl.DataFrame:
        """Filter options by strike range"""
        try:
            if len(strike_range) != 2:
                return df
            
            min_range, max_range = strike_range
            
            # Parse percentage ranges
            min_pct = self._parse_percentage_range(min_range)
            max_pct = self._parse_percentage_range(max_range)
            
            if min_pct is not None and max_pct is not None:
                min_strike = current_price * (1 + min_pct)
                max_strike = current_price * (1 + max_pct)
                
                df = df.filter(
                    (pl.col('strike_price') >= min_strike) & 
                    (pl.col('strike_price') <= max_strike)
                )
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to filter by strike range: {e}")
            return df
    
    def _parse_percentage_range(self, range_str: str) -> Optional[float]:
        """Parse percentage range string like '3%_OTM' to decimal"""
        try:
            if '%_OTM' in range_str:
                pct = float(range_str.replace('%_OTM', '')) / 100
                return pct
            elif '%_ITM' in range_str:
                pct = float(range_str.replace('%_ITM', '')) / 100
                return -pct
            return None
        except:
            return None
    
    async def _calculate_dte(self, df: pl.DataFrame) -> pl.DataFrame:
        """Calculate days to expiry"""
        try:
            # Check if expiry_date column exists
            if 'expiry_date' in df.columns:
                # Calculate DTE from expiry_date
                current_date = datetime.now().date()
                df = df.with_columns([
                    pl.when(pl.col('expiry_date').is_not_null())
                    .then(
                        (pl.col('expiry_date').str.strptime(pl.Date, fmt="%Y-%m-%d", strict=False) - 
                         pl.lit(current_date)).dt.total_days()
                    )
                    .otherwise(pl.lit(30))  # Default 30 days
                    .alias('dte')
                ])
            else:
                # Simplified DTE calculation - assume weekly expiries
                current_date = datetime.now().date()
                
                # Find next Thursday
                days_until_thursday = (3 - current_date.weekday()) % 7
                if days_until_thursday == 0:
                    days_until_thursday = 7  # Next Thursday if today is Thursday
                
                # Vary DTE between weekly (7 days) and monthly (30 days) for diversity
                estimated_dte_weekly = days_until_thursday
                estimated_dte_monthly = 30
                
                # Assign different DTEs to different strikes for variety
                df = df.with_columns([
                    pl.when(pl.col('strike_price') % 100 == 0)
                    .then(pl.lit(estimated_dte_monthly))  # Monthly for round strikes
                    .otherwise(pl.lit(estimated_dte_weekly))  # Weekly for others
                    .alias('dte')
                ])
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to calculate DTE: {e}")
            # Fallback: assign default DTE
            df = df.with_columns(pl.lit(30).alias('dte'))
            return df
    
    async def _estimate_delta(self, df: pl.DataFrame) -> pl.DataFrame:
        """Estimate delta based on moneyness"""
        try:
            # More sophisticated delta estimation based on strike price and option type
            if 'strike_price' not in df.columns or 'option_type' not in df.columns:
                # Fallback to simple estimation
                df = df.with_columns(
                    pl.when(pl.col('option_type') == 'CE')
                    .then(pl.lit(0.4))
                    .otherwise(pl.lit(-0.4))
                    .alias('delta')
                )
                return df
            
            # Estimate delta based on moneyness for each row
            # This is a simplified Black-Scholes approximation
            df = df.with_columns([
                # Calculate moneyness ratio
                (pl.col('strike_price') / 24000.0).alias('moneyness_ratio'),  # Assume base price of 24000
                
                # Estimate delta for calls
                pl.when(pl.col('option_type') == 'CE')
                .then(
                    pl.when(pl.col('strike_price') / 24000.0 < 0.95).then(pl.lit(0.8))  # Deep ITM
                    .when(pl.col('strike_price') / 24000.0 < 1.0).then(pl.lit(0.6))     # ITM
                    .when(pl.col('strike_price') / 24000.0 < 1.05).then(pl.lit(0.4))    # Slightly OTM
                    .when(pl.col('strike_price') / 24000.0 < 1.1).then(pl.lit(0.25))    # OTM
                    .otherwise(pl.lit(0.15))  # Far OTM
                )
                # Estimate delta for puts
                .otherwise(
                    pl.when(pl.col('strike_price') / 24000.0 > 1.05).then(pl.lit(-0.8))  # Deep ITM
                    .when(pl.col('strike_price') / 24000.0 > 1.0).then(pl.lit(-0.6))     # ITM
                    .when(pl.col('strike_price') / 24000.0 > 0.95).then(pl.lit(-0.4))    # Slightly OTM
                    .when(pl.col('strike_price') / 24000.0 > 0.9).then(pl.lit(-0.25))    # OTM
                    .otherwise(pl.lit(-0.15))  # Far OTM
                )
                .alias('delta')
            ])
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to estimate delta: {e}")
            # Fallback to simple estimation
            df = df.with_columns(
                pl.when(pl.col('option_type') == 'CE')
                .then(pl.lit(0.4))
                .otherwise(pl.lit(-0.4))
                .alias('delta')
            )
            return df
    
    async def _select_best_option(self, filtered_options: pl.DataFrame, criteria, 
                                current_price: float) -> Optional[SelectedOption]:
        """Select the best option from filtered results"""
        try:
            if filtered_options.is_empty():
                logger.debug("No filtered options available for selection")
                return None
            
            logger.debug(f"Selecting best option from {filtered_options.height} candidates")
            
            # Sort by volume and open interest (liquidity preference)
            # Handle missing columns gracefully
            sort_columns = []
            if 'volume' in filtered_options.columns:
                sort_columns.append('volume')
            if 'open_interest' in filtered_options.columns:
                sort_columns.append('open_interest')
            
            if sort_columns:
                sorted_options = filtered_options.sort(sort_columns, descending=True)
            else:
                # If no volume/OI columns, sort by strike price (closest to ATM first)
                if 'strike_price' in filtered_options.columns:
                    # Add distance from current price for sorting
                    sorted_options = filtered_options.with_columns(
                        (pl.col('strike_price') - current_price).abs().alias('distance_from_atm')
                    ).sort('distance_from_atm')
                else:
                    sorted_options = filtered_options
            
            # Take the best option
            best_row = sorted_options.head(1).row(0, named=True)
            
            # Determine premium from available columns
            premium = (
                best_row.get('premium') or 
                best_row.get('ltp') or 
                best_row.get('close') or 
                best_row.get('last_price') or 
                50.0  # Default premium for backtesting
            )
            
            # Create SelectedOption object with robust field extraction
            selected_option = SelectedOption(
                symbol=best_row.get('symbol', f"{best_row.get('underlying', 'UNK')}_{int(best_row.get('strike_price', 0))}_{best_row.get('option_type', 'CE')}"),
                underlying=best_row.get('underlying', 'UNKNOWN'),
                strike_price=float(best_row.get('strike_price', 0.0)),
                option_type=best_row.get('option_type', 'CE'),
                premium=float(premium),
                volume=int(best_row.get('volume', 1000)),  # Default volume for backtesting
                open_interest=int(best_row.get('open_interest', 500)),  # Default OI for backtesting
                delta=best_row.get('delta'),
                dte=best_row.get('dte'),
                quantity=1
            )
            
            # Calculate moneyness
            selected_option.moneyness = self._calculate_moneyness(
                selected_option.strike_price, 
                selected_option.option_type, 
                current_price
            )
            
            logger.debug(f"Selected option: {selected_option.symbol} at strike {selected_option.strike_price} with premium {selected_option.premium}")
            return selected_option
            
        except Exception as e:
            logger.error(f"Failed to select best option: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None
    
    async def _ensure_required_columns(self, df: pl.DataFrame) -> pl.DataFrame:
        """Ensure required columns exist with appropriate defaults"""
        try:
            # Map common column variations
            column_mapping = {
                'ltp': 'premium',
                'close': 'premium', 
                'last_price': 'premium',
                'vol': 'volume',
                'oi': 'open_interest'
            }
            
            # Rename columns if they exist
            for old_col, new_col in column_mapping.items():
                if old_col in df.columns and new_col not in df.columns:
                    df = df.rename({old_col: new_col})
            
            # Add missing required columns with defaults
            required_columns = {
                'premium': 50.0,
                'volume': 1000,
                'open_interest': 500,
                'option_type': 'CE',
                'strike_price': 24000.0
            }
            
            for col, default_val in required_columns.items():
                if col not in df.columns:
                    df = df.with_columns(pl.lit(default_val).alias(col))
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to ensure required columns: {e}")
            return df
    
    def _calculate_moneyness(self, strike_price: float, option_type: str, current_price: float) -> str:
        """Calculate moneyness classification"""
        try:
            ratio = strike_price / current_price

            if option_type == 'CE':
                if ratio < 0.98:
                    return 'ITM'
                elif ratio <= 1.02:
                    return 'ATM'
                else:
                    return 'OTM'
            else:  # PE
                if ratio > 1.02:
                    return 'ITM'
                elif ratio >= 0.98:
                    return 'ATM'
                else:
                    return 'OTM'

        except:
            return 'UNKNOWN'

# JIT-compiled functions for performance-critical calculations
@njit(cache=True, fastmath=True) if NUMBA_AVAILABLE else lambda x: x
def fast_moneyness_calculation(strike_prices: np.ndarray, option_types: np.ndarray,
                              current_price: float) -> np.ndarray:
    """Fast moneyness calculation using Numba JIT"""
    n = len(strike_prices)
    moneyness = np.empty(n, dtype=np.int8)  # 0=ITM, 1=ATM, 2=OTM

    for i in prange(n):
        ratio = strike_prices[i] / current_price

        if option_types[i] == 0:  # CE (0 for CE, 1 for PE)
            if ratio < 0.98:
                moneyness[i] = 0  # ITM
            elif ratio <= 1.02:
                moneyness[i] = 1  # ATM
            else:
                moneyness[i] = 2  # OTM
        else:  # PE
            if ratio > 1.02:
                moneyness[i] = 0  # ITM
            elif ratio >= 0.98:
                moneyness[i] = 1  # ATM
            else:
                moneyness[i] = 2  # OTM

    return moneyness

@njit(cache=True, fastmath=True) if NUMBA_AVAILABLE else lambda x: x
def fast_delta_estimation(strike_prices: np.ndarray, option_types: np.ndarray,
                         current_price: float) -> np.ndarray:
    """Fast delta estimation using Numba JIT"""
    n = len(strike_prices)
    deltas = np.empty(n, dtype=np.float64)

    for i in prange(n):
        ratio = strike_prices[i] / current_price

        if option_types[i] == 0:  # CE
            if ratio < 0.95:
                deltas[i] = 0.8  # Deep ITM
            elif ratio < 1.0:
                deltas[i] = 0.6  # ITM
            elif ratio < 1.05:
                deltas[i] = 0.4  # Slightly OTM
            elif ratio < 1.1:
                deltas[i] = 0.25  # OTM
            else:
                deltas[i] = 0.15  # Far OTM
        else:  # PE
            if ratio > 1.05:
                deltas[i] = -0.8  # Deep ITM
            elif ratio > 1.0:
                deltas[i] = -0.6  # ITM
            elif ratio > 0.95:
                deltas[i] = -0.4  # Slightly OTM
            elif ratio > 0.9:
                deltas[i] = -0.25  # OTM
            else:
                deltas[i] = -0.15  # Far OTM

    return deltas

@njit(cache=True, fastmath=True) if NUMBA_AVAILABLE else lambda x: x
def fast_option_scoring(volumes: np.ndarray, open_interests: np.ndarray,
                       distances_from_atm: np.ndarray) -> np.ndarray:
    """Fast option scoring for selection using Numba JIT"""
    n = len(volumes)
    scores = np.empty(n, dtype=np.float64)

    # Normalize factors
    max_volume = np.max(volumes) if np.max(volumes) > 0 else 1.0
    max_oi = np.max(open_interests) if np.max(open_interests) > 0 else 1.0
    max_distance = np.max(distances_from_atm) if np.max(distances_from_atm) > 0 else 1.0

    for i in prange(n):
        # Weighted scoring: 40% volume, 40% OI, 20% proximity to ATM
        volume_score = volumes[i] / max_volume
        oi_score = open_interests[i] / max_oi
        proximity_score = 1.0 - (distances_from_atm[i] / max_distance)

        scores[i] = 0.4 * volume_score + 0.4 * oi_score + 0.2 * proximity_score

    return scores
"""
Configuration and result data structures for the backtesting engine.
"""
from dataclasses import dataclass, field
from typing import Dict, List, Optional

@dataclass
class BacktestConfig:
    """Backtesting configuration"""
    start_date: str
    end_date: str
    initial_capital: float = 100000.0
    risk_per_trade: float = 0.02  # 2% risk per trade
    transaction_cost: float = 20.0  # Per lot transaction cost
    slippage: float = 0.01  # 1% slippage
    margin_multiplier: float = 1.0

@dataclass
class BacktestResults:
    """Enhanced backtesting results with additional fields"""
    strategy_id: str
    total_return: float
    annualized_return: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    total_trades: int
    avg_trade_return: float
    best_trade: float
    worst_trade: float
    avg_holding_period: float
    greeks_pnl: Dict[str, float]
    volatility_pnl: float
    time_decay_pnl: float
    underlying: str = "NIFTY"
    timeframe: str = "1min"
    winning_trades: int = 0
    losing_trades: int = 0
    max_consecutive_losses: int = 0
    final_portfolio_value: float = 0.0
    trades: Optional[List[Dict]] = field(default_factory=list)

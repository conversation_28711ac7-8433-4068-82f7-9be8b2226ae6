#!/usr/bin/env python3
"""
Indian Options Data Loader - Specialized loader for Indian options backtesting data

This module handles loading and processing of Indian options data including:
1. NIFTY and BANKNIFTY index data
2. Options chain data (CE/PE strikes)
3. Feature-engineered data integration
4. Proper data alignment for backtesting
"""

import logging
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import polars as pl
import re

logger = logging.getLogger(__name__)

class IndianOptionsDataLoader:
    """Specialized data loader for Indian options backtesting"""
    
    def __init__(self):
        self.data_path = Path("data")
        self.historical_path = self.data_path / "historical"
        self.features_path = self.data_path / "features"
        
    async def load_integrated_backtest_data(self, start_date: str, end_date: str, 
                                          timeframes: List[str] = None) -> Tuple[pl.DataFrame, Dict[str, pl.DataFrame]]:
        """
        Load integrated data for backtesting including index data and options chains
        
        Returns:
            Tuple of (historical_data, option_chains_by_underlying)
        """
        if timeframes is None:
            timeframes = ['5min']  # Use only 5min data for faster processing
            
        try:
            # Load index data (underlying prices)
            historical_data = await self._load_index_data(start_date, end_date, timeframes)
            
            # Load options chain data
            option_chains = await self._load_options_chain_data(start_date, end_date, timeframes)
            
            # Integrate with feature data if available
            feature_data = await self._load_feature_data(start_date, end_date, timeframes)
            if feature_data is not None and not feature_data.is_empty():
                historical_data = await self._integrate_feature_data(historical_data, feature_data)
            
            logger.info(f"Loaded integrated data: {historical_data.height} historical records, "
                       f"{sum(df.height for df in option_chains.values())} option records")
            
            return historical_data, option_chains
            
        except Exception as e:
            logger.error(f"Failed to load integrated backtest data: {e}")
            return pl.DataFrame(), {}
    
    async def _load_index_data(self, start_date: str, end_date: str, timeframes: List[str]) -> pl.DataFrame:
        """Load NIFTY and BANKNIFTY index data"""
        all_index_data = []
        
        for timeframe in timeframes:
            timeframe_path = self.historical_path / timeframe
            if not timeframe_path.exists():
                continue
                
            # Look for index files
            index_files = list(timeframe_path.glob("Index_*.parquet"))
            
            for file in index_files:
                try:
                    # Extract underlying from filename
                    underlying = self._extract_underlying_from_index_file(file.name)
                    if not underlying:
                        continue
                        
                    df = pl.read_parquet(file)
                    
                    # Standardize columns
                    df = self._standardize_index_columns(df, underlying, timeframe)
                    
                    # Filter by date range
                    df = self._filter_by_date_range(df, start_date, end_date)
                    
                    if not df.is_empty():
                        all_index_data.append(df)
                        
                except Exception as e:
                    logger.warning(f"Failed to load index file {file}: {e}")
        
        if not all_index_data:
            logger.warning("No index data found")
            return pl.DataFrame()
            
        # Combine all index data
        combined_data = pl.concat(all_index_data, how="diagonal")
        
        # Sort by timestamp
        combined_data = combined_data.sort(['underlying', 'timestamp'])
        
        logger.info(f"Loaded {combined_data.height} index data records")
        return combined_data
    
    async def _load_options_chain_data(self, start_date: str, end_date: str, 
                                     timeframes: List[str]) -> Dict[str, pl.DataFrame]:
        """Load options chain data for NIFTY and BANKNIFTY"""
        option_chains = {'NIFTY': [], 'BANKNIFTY': []}
        
        for timeframe in timeframes:
            timeframe_path = self.historical_path / timeframe
            if not timeframe_path.exists():
                continue
                
            # Look for option files - limit to reduce processing time
            option_files = list(timeframe_path.glob("*_CE_*.parquet"))[:20] + list(timeframe_path.glob("*_PE_*.parquet"))[:20]
            
            for file in option_files:
                try:
                    # Extract underlying and option details from filename
                    filename_underlying, strike, option_type = self._extract_option_details(file.name)
                    if not filename_underlying:
                        continue

                    df = pl.read_parquet(file)

                    # Standardize columns
                    df = self._standardize_option_columns(df, filename_underlying, strike, option_type, timeframe)

                    # Filter by date range
                    df = self._filter_by_date_range(df, start_date, end_date)

                    if not df.is_empty():
                        # Use the actual underlying from the data, not the filename
                        actual_underlying = df.select('underlying').to_series().unique()[0]
                        if actual_underlying in option_chains:
                            option_chains[actual_underlying].append(df)
                        
                except Exception as e:
                    logger.warning(f"Failed to load option file {file}: {e}")
        
        # Combine data for each underlying
        combined_chains = {}
        for underlying, data_list in option_chains.items():
            if data_list:
                combined_df = pl.concat(data_list, how="diagonal")
                combined_df = combined_df.sort(['date', 'strike_price', 'option_type'])
                combined_chains[underlying] = combined_df
                logger.info(f"Loaded {combined_df.height} option records for {underlying}")
            else:
                logger.warning(f"No option data found for {underlying}")
                
        return combined_chains
    
    async def _load_feature_data(self, start_date: str, end_date: str, 
                               timeframes: List[str]) -> Optional[pl.DataFrame]:
        """Load feature-engineered data if available"""
        all_feature_data = []
        
        for timeframe in timeframes:
            timeframe_path = self.features_path / timeframe
            if not timeframe_path.exists():
                continue
                
            feature_files = list(timeframe_path.glob("*.parquet"))
            
            for file in feature_files:
                try:
                    df = pl.read_parquet(file)
                    
                    # Add timeframe info
                    df = df.with_columns(pl.lit(timeframe).alias('timeframe'))
                    
                    # Filter by date range if timestamp column exists
                    if 'timestamp' in df.columns:
                        df = self._filter_by_date_range(df, start_date, end_date)
                    
                    if not df.is_empty():
                        all_feature_data.append(df)
                        
                except Exception as e:
                    logger.warning(f"Failed to load feature file {file}: {e}")
        
        if not all_feature_data:
            return None
            
        combined_features = pl.concat(all_feature_data, how="diagonal")
        logger.info(f"Loaded {combined_features.height} feature records")
        return combined_features
    
    def _extract_underlying_from_index_file(self, filename: str) -> Optional[str]:
        """Extract underlying from index filename"""
        match = re.search(r'Index_([A-Z]+)_', filename)
        return match.group(1) if match else None
    
    def _extract_option_details(self, filename: str) -> Tuple[Optional[str], Optional[float], Optional[str]]:
        """Extract underlying, strike, and option type from option filename"""
        # Pattern: UNDERLYING_STRIKE_TYPE_TIMEFRAME_TIMESTAMP.parquet
        match = re.search(r'([A-Z]+)_(\d+(?:\.\d+)?)_(CE|PE)_', filename)
        if match:
            underlying = match.group(1)
            strike = float(match.group(2))
            option_type = match.group(3)
            return underlying, strike, option_type
        return None, None, None
    
    def _standardize_index_columns(self, df: pl.DataFrame, underlying: str, timeframe: str) -> pl.DataFrame:
        """Standardize index data columns"""
        # Common column mappings for index data
        column_mapping = {
            'datetime': 'timestamp',
            'Date': 'timestamp',
            'Time': 'time',
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close',
            'Volume': 'volume',
            'LTP': 'close',
            'ltp': 'close'
        }
        
        # Rename columns
        for old_col, new_col in column_mapping.items():
            if old_col in df.columns:
                df = df.rename({old_col: new_col})
        
        # Add metadata
        df = df.with_columns([
            pl.lit(underlying).alias('underlying'),
            pl.lit(timeframe).alias('timeframe'),
            pl.lit('INDEX').alias('instrument_type')
        ])
        
        # Ensure timestamp column
        if 'timestamp' not in df.columns:
            if 'date' in df.columns and 'time' in df.columns:
                df = df.with_columns(
                    (pl.col('date').cast(pl.Utf8) + " " + pl.col('time').cast(pl.Utf8))
                    .str.to_datetime()
                    .alias('timestamp')
                )
            else:
                # Use current time as fallback
                df = df.with_columns(pl.lit(datetime.now(timezone.utc)).alias('timestamp'))
        
        # Ensure required columns exist
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        for col in required_cols:
            if col not in df.columns:
                if col == 'volume':
                    df = df.with_columns(pl.lit(0).alias(col))
                else:
                    # Use close price as fallback for OHLC
                    close_col = 'close' if 'close' in df.columns else 'ltp'
                    if close_col in df.columns:
                        df = df.with_columns(pl.col(close_col).alias(col))
                    else:
                        df = df.with_columns(pl.lit(0.0).alias(col))
        
        return df
    
    def _standardize_option_columns(self, df: pl.DataFrame, underlying: str, 
                                  strike: float, option_type: str, timeframe: str) -> pl.DataFrame:
        """Standardize option data columns"""
        # Common column mappings for option data
        column_mapping = {
            'datetime': 'timestamp',
            'Date': 'date',
            'Time': 'time',
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close',
            'Volume': 'volume',
            'LTP': 'close',
            'ltp': 'close',
            'OI': 'open_interest',
            'OpenInterest': 'open_interest'
        }
        
        # Rename columns
        for old_col, new_col in column_mapping.items():
            if old_col in df.columns:
                df = df.rename({old_col: new_col})
        
        # Use the underlying from the data if available, otherwise use filename
        if 'underlying' in df.columns:
            # Keep the existing underlying from data
            pass
        else:
            df = df.with_columns(pl.lit(underlying).alias('underlying'))

        # Add other metadata
        df = df.with_columns([
            pl.lit(strike).alias('strike_price'),
            pl.lit(option_type).alias('option_type'),
            pl.lit(timeframe).alias('timeframe'),
            pl.lit('OPTION').alias('instrument_type')
        ])
        
        # Create symbol using actual underlying from data
        df = df.with_columns(
            (pl.col('underlying') + "_" + pl.lit(str(int(strike))) + "_" + pl.lit(option_type)).alias('symbol')
        )
        
        # Ensure date column for options
        if 'date' not in df.columns:
            if 'timestamp' in df.columns:
                df = df.with_columns(pl.col('timestamp').dt.date().alias('date'))
            else:
                df = df.with_columns(pl.lit(datetime.now().date()).alias('date'))
        
        # Ensure required columns
        required_cols = ['open', 'high', 'low', 'close', 'volume', 'open_interest']
        for col in required_cols:
            if col not in df.columns:
                if col in ['volume', 'open_interest']:
                    df = df.with_columns(pl.lit(0).alias(col))
                else:
                    # Use close price as fallback
                    close_col = 'close' if 'close' in df.columns else 'ltp'
                    if close_col in df.columns:
                        df = df.with_columns(pl.col(close_col).alias(col))
                    else:
                        df = df.with_columns(pl.lit(0.0).alias(col))
        
        # Add premium column (alias for close)
        df = df.with_columns(pl.col('close').alias('premium'))

        # Add DTE (Days to Expiry) - for weekly options, assume expiry is Thursday of the same week
        if 'dte' not in df.columns:
            df = df.with_columns([
                # Calculate days until next Thursday (weekly expiry)
                # For simplicity, use a fixed DTE based on weekday
                pl.when(pl.col('timestamp').dt.weekday() <= 4)  # Monday to Thursday
                .then(5 - pl.col('timestamp').dt.weekday())     # Days until Thursday
                .otherwise(7 - pl.col('timestamp').dt.weekday() + 4)  # Days until next Thursday
                .alias('dte')
            ])

        return df
    
    def _filter_by_date_range(self, df: pl.DataFrame, start_date: str, end_date: str) -> pl.DataFrame:
        """Filter dataframe by date range"""
        try:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
            end_dt = datetime.strptime(end_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
            
            if 'timestamp' in df.columns:
                # Ensure timestamp is datetime type
                if df['timestamp'].dtype != pl.Datetime:
                    df = df.with_columns(pl.col('timestamp').cast(pl.Datetime))
                
                df = df.filter(
                    (pl.col('timestamp') >= start_dt) & 
                    (pl.col('timestamp') <= end_dt)
                )
            elif 'date' in df.columns:
                # Filter by date column
                start_date_only = start_dt.date()
                end_date_only = end_dt.date()
                
                df = df.filter(
                    (pl.col('date') >= start_date_only) & 
                    (pl.col('date') <= end_date_only)
                )
            
            return df
            
        except Exception as e:
            logger.warning(f"Failed to filter by date range: {e}")
            return df
    
    async def _integrate_feature_data(self, historical_data: pl.DataFrame, 
                                    feature_data: pl.DataFrame) -> pl.DataFrame:
        """Integrate feature data with historical data"""
        try:
            # Try to join on common columns
            join_cols = []
            
            if 'underlying' in historical_data.columns and 'underlying' in feature_data.columns:
                join_cols.append('underlying')
            
            if 'timestamp' in historical_data.columns and 'timestamp' in feature_data.columns:
                join_cols.append('timestamp')
            elif 'date' in historical_data.columns and 'date' in feature_data.columns:
                join_cols.append('date')
            
            if join_cols:
                # Perform left join to preserve all historical data
                integrated_data = historical_data.join(
                    feature_data, 
                    on=join_cols, 
                    how='left'
                )
                logger.info(f"Integrated feature data using columns: {join_cols}")
                return integrated_data
            else:
                logger.warning("No common columns found for feature integration")
                return historical_data
                
        except Exception as e:
            logger.warning(f"Failed to integrate feature data: {e}")
            return historical_data
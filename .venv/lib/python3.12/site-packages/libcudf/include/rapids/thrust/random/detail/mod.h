/*
 *  Copyright 2008-2013 NVIDIA Corporation
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

#pragma once

#include <thrust/detail/config.h>

#if defined(_CCCL_IMPLICIT_SYSTEM_HEADER_GCC)
#  pragma GCC system_header
#elif defined(_CCCL_IMPLICIT_SYSTEM_HEADER_CLANG)
#  pragma clang system_header
#elif defined(_CCCL_IMPLICIT_SYSTEM_HEADER_MSVC)
#  pragma system_header
#endif // no system header

THRUST_NAMESPACE_BEGIN

namespace random
{

namespace detail
{

template <typename T, T a, T c, T m, bool = (m == 0)>
struct static_mod
{
  static const T q = m / a;
  static const T r = m % a;

  _CCCL_HOST_DEVICE T operator()(T x) const
  {
    if constexpr (a == 1)
    {
      x %= m;
    }
    else
    {
      T t1 = a * (x % q);
      T t2 = r * (x / q);
      if (t1 >= t2)
      {
        x = t1 - t2;
      }
      else
      {
        x = m - t2 + t1;
      }
    }

    if constexpr (c != 0)
    {
      const T d = m - x;
      if (d > c)
      {
        x += c;
      }
      else
      {
        x = c - d;
      }
    }

    return x;
  }
}; // end static_mod

// Rely on machine overflow handling
template <typename T, T a, T c, T m>
struct static_mod<T, a, c, m, true>
{
  _CCCL_HOST_DEVICE T operator()(T x) const
  {
    return a * x + c;
  }
}; // end static_mod

template <typename T, T a, T c, T m>
_CCCL_HOST_DEVICE T mod(T x)
{
  static_mod<T, a, c, m> f;
  return f(x);
} // end static_mod

} // namespace detail

} // namespace random

THRUST_NAMESPACE_END

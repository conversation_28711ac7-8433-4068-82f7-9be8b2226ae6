# Copyright (c) 2024, NVIDIA CORPORATION.

from pylibcudf.column import Column
from pylibcudf.types import DataType

def to_integers(input: Column, output_type: DataType) -> Column: ...
def from_integers(integers: Column) -> Column: ...
def is_integer(input: Column, int_type: DataType | None = None) -> Column: ...
def hex_to_integers(input: Column, output_type: DataType) -> Column: ...
def is_hex(input: Column) -> Column: ...
def integers_to_hex(input: Column) -> Column: ...

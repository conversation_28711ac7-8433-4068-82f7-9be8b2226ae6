# Copyright (c) 2024, NVIDIA CORPORATION.

from typing import overload

from pylibcudf.column import Column
from pylibcudf.scalar import <PERSON>ala<PERSON>
from pylibcudf.strings.regex_flags import RegexFlags
from pylibcudf.strings.regex_program import Reg<PERSON><PERSON><PERSON>ram

@overload
def replace_re(
    input: <PERSON>umn,
    pattern: <PERSON><PERSON><PERSON>rogram,
    replacement: <PERSON><PERSON><PERSON>,
    max_replace_count: int = -1,
) -> Column: ...
@overload
def replace_re(
    input: Column,
    patterns: list[str],
    replacement: Column,
    max_replace_count: int = -1,
    flags: RegexFlags = RegexFlags.DEFAULT,
) -> Column: ...
def replace_with_backrefs(
    input: Column, prog: RegexProgram, replacement: str
) -> Column: ...

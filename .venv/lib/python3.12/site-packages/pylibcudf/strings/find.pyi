# Copyright (c) 2024, NVIDIA CORPORATION.

from pylibcudf.column import Column
from pylibcudf.scalar import <PERSON>alar

def find(
    input: Column, target: Column | Scalar, start: int = 0, stop: int = -1
) -> Column: ...
def rfind(
    input: Column, target: Scalar, start: int = 0, stop: int = -1
) -> Column: ...
def contains(input: Column, target: Column | Scalar) -> Column: ...
def starts_with(input: Column, target: Column | Scalar) -> Column: ...
def ends_with(input: Column, target: Column | Scalar) -> Column: ...

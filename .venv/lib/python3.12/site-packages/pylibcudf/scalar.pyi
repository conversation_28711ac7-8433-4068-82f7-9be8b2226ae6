# Copyright (c) 2024-2025, NVIDIA CORPORATION.

from typing import Any

from pylibcudf.column import Column
from pylibcudf.types import DataType

NpGeneric = type[Any]

class Scalar:
    def __init__(self): ...
    def type(self) -> DataType: ...
    def is_valid(self) -> bool: ...
    @staticmethod
    def empty_like(column: Column) -> Scalar: ...
    @staticmethod
    def from_arrow(pa_val: Any, dtype: DataType | None = None) -> Scalar: ...
    @classmethod
    def from_py(cls, py_val: Any, dtype: DataType | None = None) -> Scalar: ...
    @classmethod
    def from_numpy(cls, np_val: NpGeneric) -> Scalar: ...
    def to_py(self) -> None | int | float | str | bool: ...

# Copyright (c) 2024, NVIDIA CORPORATION.

from pylibcudf.types import DataType

def is_relationally_comparable(typ: DataType) -> bool: ...
def is_equality_comparable(typ: DataType) -> bool: ...
def is_numeric(typ: DataType) -> bool: ...
def is_numeric_not_bool(typ: DataType) -> bool: ...
def is_index_type(typ: DataType) -> bool: ...
def is_unsigned(typ: DataType) -> bool: ...
def is_integral(typ: DataType) -> bool: ...
def is_integral_not_bool(typ: DataType) -> bool: ...
def is_floating_point(typ: DataType) -> bool: ...
def is_boolean(typ: DataType) -> bool: ...
def is_timestamp(typ: DataType) -> bool: ...
def is_fixed_point(typ: DataType) -> bool: ...
def is_duration(typ: DataType) -> bool: ...
def is_chrono(typ: DataType) -> bool: ...
def is_dictionary(typ: DataType) -> bool: ...
def is_fixed_width(typ: DataType) -> bool: ...
def is_compound(typ: DataType) -> bool: ...
def is_nested(typ: DataType) -> bool: ...
def is_bit_castable(source: DataType, target: DataType) -> bool: ...

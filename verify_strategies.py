#!/usr/bin/env python3
"""
Simple verification script for optimized strategies
"""
import json
from pathlib import Path

def verify_strategies():
    """Verify the optimized strategies file"""
    try:
        strategy_file = Path("data/strategies/optimized_strategies.json")
        
        if not strategy_file.exists():
            print(f"ERROR: Strategy file not found at {strategy_file}")
            return False
        
        with open(strategy_file, 'r') as f:
            strategies = json.load(f)
        
        print(f"✓ Loaded {len(strategies)} strategies")
        
        required_fields = ['strategy_id', 'strategy_type', 'underlying', 'name', 
                          'selection_criteria', 'entry_conditions', 'exit_conditions',
                          'risk_management', 'created_at', 'tags']
        
        for i, strategy in enumerate(strategies):
            print(f"\n--- Strategy {i+1}: {strategy.get('strategy_id', 'UNKNOWN')} ---")
            
            # Check required fields
            missing_fields = [field for field in required_fields if field not in strategy]
            if missing_fields:
                print(f"✗ Missing fields: {missing_fields}")
                return False
            else:
                print("✓ All required fields present")
            
            # Check entry conditions use simple indicators
            entry_conditions = strategy.get('entry_conditions', [])
            indicators = [cond.get('indicator') for cond in entry_conditions]
            print(f"✓ Entry indicators: {indicators}")
            
            # Check selection criteria
            selection_criteria = strategy.get('selection_criteria', {})
            for leg_name, criteria in selection_criteria.items():
                dte_range = criteria.get('dte_range', [])
                volume_threshold = criteria.get('volume_threshold', 0)
                oi_threshold = criteria.get('open_interest_threshold', 0)
                print(f"✓ {leg_name}: DTE {dte_range}, Vol≥{volume_threshold}, OI≥{oi_threshold}")
        
        print(f"\n✓ All {len(strategies)} strategies verified successfully!")
        print("\nKey optimizations:")
        print("- Simple indicators (close, sma_10, volume)")
        print("- Short DTE ranges (1-7 days)")
        print("- Low volume/OI thresholds")
        print("- Index-synchronized strategies")
        
        return True
        
    except Exception as e:
        print(f"ERROR: {e}")
        return False

if __name__ == "__main__":
    success = verify_strategies()
    print(f"\nVerification {'PASSED' if success else 'FAILED'}")
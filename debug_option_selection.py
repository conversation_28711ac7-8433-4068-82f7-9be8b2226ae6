#!/usr/bin/env python3
"""Debug script to understand why options are not being selected"""

import asyncio
import polars as pl
from pathlib import Path
from agents.backtesting.indian_options_data_loader import IndianOptionsDataLoader
from agents.signal_generation.option_selector import OptionSelector
from agents.backtesting.strategy_definition_backtester import StrategyDefinition
import json

async def debug_option_selection():
    print("=== DEBUGGING OPTION SELECTION ===")
    
    # Load strategy definitions
    with open('data/strategies/optimized_strategies.json', 'r') as f:
        strategies = json.load(f)
    
    strategy = strategies[0]  # First strategy
    print(f"Strategy: {strategy['strategy_id']}")
    print(f"Underlying: {strategy['underlying']}")
    print(f"Selection criteria: {strategy['selection_criteria']}")
    
    # Load data using the same loader as backtesting
    loader = IndianOptionsDataLoader()
    
    # Load option data
    option_data = await loader._load_options_chain_data(
        start_date='2025-08-07',
        end_date='2025-08-13',
        timeframes=['15min']
    )
    
    if 'NIFTY' not in option_data:
        print("ERROR: No NIFTY option data loaded!")
        return
    
    nifty_options = option_data['NIFTY']
    print(f"\nOption data loaded: {nifty_options.height} records")
    print(f"Columns: {nifty_options.columns}")
    
    # Check sample data
    print("\nSample option data:")
    print(nifty_options.head(3))
    
    # Check data ranges
    print(f"\nData analysis:")
    print(f"Date range: {nifty_options.select('date').to_series().unique().sort()}")
    print(f"Option types: {nifty_options.select('option_type').to_series().unique()}")
    print(f"Strike prices: {sorted(nifty_options.select('strike_price').to_series().unique())}")
    
    if 'dte' in nifty_options.columns:
        print(f"DTE range: {nifty_options.select('dte').to_series().min()} to {nifty_options.select('dte').to_series().max()}")
    else:
        print("ERROR: No DTE column found!")
    
    if 'volume' in nifty_options.columns:
        print(f"Volume range: {nifty_options.select('volume').to_series().min()} to {nifty_options.select('volume').to_series().max()}")
    
    if 'open_interest' in nifty_options.columns:
        print(f"OI range: {nifty_options.select('open_interest').to_series().min()} to {nifty_options.select('open_interest').to_series().max()}")
    
    # Test option selection
    print("\n=== TESTING OPTION SELECTION ===")
    
    # Create option selector
    option_selector = OptionSelector()
    
    # Create strategy definition object
    strategy_def = StrategyDefinition(
        strategy_id=strategy['strategy_id'],
        strategy_type=strategy['strategy_type'],
        underlying=strategy['underlying'],
        name=strategy['name'],
        description=strategy['description'],
        selection_criteria=strategy['selection_criteria'],
        entry_conditions=strategy['entry_conditions'],
        exit_conditions=strategy['exit_conditions'],
        risk_management=strategy['risk_management'],
        market_outlook=strategy['market_outlook'],
        volatility_outlook=strategy['volatility_outlook'],
        timeframe=strategy['timeframe'],
        created_at=strategy['created_at'],
        tags=strategy['tags']
    )
    
    # Get a sample of option data for one date
    sample_date = nifty_options.select('date').to_series().unique().sort()[0]
    daily_options = nifty_options.filter(pl.col('date') == sample_date)
    print(f"\nTesting with {daily_options.height} options for date {sample_date}")
    
    # Test with a sample current price
    current_price = 24000.0  # Approximate NIFTY level
    print(f"Using current price: {current_price}")
    
    # Try to select options
    selected_options = await option_selector.select_options_for_strategy(
        strategy_def, daily_options, current_price
    )
    
    print(f"\nSelected options: {len(selected_options) if selected_options else 0}")
    
    if not selected_options:
        print("No options selected. Let's debug the filtering...")
        
        # Test each filter step by step
        call_criteria = strategy['selection_criteria']['call_leg']
        print(f"\nCall criteria: {call_criteria}")
        
        # Filter by option type
        calls = daily_options.filter(pl.col('option_type') == 'CE')
        print(f"After option type filter (CE): {calls.height} options")
        
        # Filter by DTE
        if 'dte_range' in call_criteria:
            dte_min, dte_max = call_criteria['dte_range']
            calls_dte = calls.filter(
                (pl.col('dte') >= dte_min) & (pl.col('dte') <= dte_max)
            )
            print(f"After DTE filter ({dte_min}-{dte_max}): {calls_dte.height} options")
            if calls_dte.height > 0:
                print(f"DTE values in filtered data: {calls_dte.select('dte').to_series().unique().sort()}")
        
        # Filter by volume
        if 'volume_threshold' in call_criteria:
            vol_threshold = call_criteria['volume_threshold']
            calls_vol = calls_dte.filter(pl.col('volume') >= vol_threshold)
            print(f"After volume filter (>= {vol_threshold}): {calls_vol.height} options")
            if calls_vol.height > 0:
                print(f"Volume values in filtered data: {calls_vol.select('volume').to_series().unique().sort()}")
        
        # Filter by OI
        if 'open_interest_threshold' in call_criteria:
            oi_threshold = call_criteria['open_interest_threshold']
            calls_oi = calls_vol.filter(pl.col('open_interest') >= oi_threshold)
            print(f"After OI filter (>= {oi_threshold}): {calls_oi.height} options")
            if calls_oi.height > 0:
                print(f"OI values in filtered data: {calls_oi.select('open_interest').to_series().unique().sort()}")

if __name__ == "__main__":
    asyncio.run(debug_option_selection())

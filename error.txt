(.venv) jmk@jmk-desktop:/media/jmk/BKP/Documents/Option$ source .venv/bin/activate && python main.py --agent backtesting
2025-08-21 12:56:29,667 - __main__ - INFO - [INIT] Options System Orchestrator initialized
2025-08-21 12:56:29,667 - __main__ - INFO - [REAL] Real trading mode enabled - SmartAPI integration active
2025-08-21 12:56:29,668 - __main__ - INFO - [PIPELINE] Recent feature engineered data found. Skipping feature engineering.
2025-08-21 12:56:29,668 - __main__ - INFO - [AGENT] Running backtesting agent...
2025-08-21 12:56:29,668 - __main__ - INFO - [AGENT] Starting backtesting agent in REAL mode...
2025-08-21 12:56:29,668 - agents.backtesting_agent - INFO - Configuration loaded successfully
2025-08-21 12:56:29,669 - agents.backtesting_agent - INFO - Loading optimized strategy definition file: optimized_strategies.json
2025-08-21 12:56:29,669 - agents.backtesting_agent - INFO - Loaded 4 strategy definitions from optimized_strategies.json
2025-08-21 12:56:29,670 - agents.backtesting_agent - INFO - Options Backtesting Agent initialized successfully
2025-08-21 12:56:29,670 - agents.backtesting_agent - INFO - Starting Options Backtesting Agent...
2025-08-21 12:56:29,670 - agents.backtesting_agent - INFO - Using strategy definitions for backtesting
2025-08-21 12:56:29,681 - agents.backtesting.indian_options_data_loader - INFO - Loaded 750 index data records
2025-08-21 12:56:29,786 - agents.backtesting.indian_options_data_loader - INFO - Loaded 14474 option records for NIFTY
2025-08-21 12:56:29,786 - agents.backtesting.indian_options_data_loader - WARNING - No option data found for BANKNIFTY
2025-08-21 12:56:30,475 - agents.backtesting.indian_options_data_loader - INFO - Loaded 3347028 feature records
2025-08-21 12:56:30,785 - agents.backtesting.indian_options_data_loader - INFO - Integrated feature data using columns: ['underlying', 'timestamp']
2025-08-21 12:56:30,785 - agents.backtesting.indian_options_data_loader - INFO - Loaded integrated data: 3306528 historical records, 14474 option records
2025-08-21 12:56:30,787 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Testing strategy definition: NIFTY_WEEKLY_MOMENTUM_CALL
2025-08-21 12:56:30,930 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Large dataset detected (2263464 records). Sampling for performance...
2025-08-21 12:56:30,951 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Sampled dataset: 226347 records
2025-08-21 12:56:30,953 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Data: 226347 historical records, 14474 option records
2025-08-21 12:56:30,953 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Date range: 2025-08-07 to 2025-08-13 (5 unique dates)
2025-08-21 12:56:30,954 - agents.backtesting.strategy_definition_backtester - INFO - [SIMULATE] Processing 3 unique dates for NIFTY_WEEKLY_MOMENTUM_CALL (limited for speed)
[DEBUG] Processing 5 sample points with step_size=8808 for 44043 records on 2025-08-07
[DEBUG] Selecting options: chain_size=2830, price=24519.45
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:56:30,957 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_CALL
[DEBUG] Selected 1 options
2025-08-21 12:56:30,957 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_CALL at 2025-08-07 03:45:00+00:00
[DEBUG] Selecting options: chain_size=2830, price=24422.2
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:56:30,959 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_CALL
[DEBUG] Selected 1 options
2025-08-21 12:56:30,959 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_CALL at 2025-08-07 06:15:00+00:00
[DEBUG] Selecting options: chain_size=2830, price=24467.3
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:56:30,961 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_CALL
[DEBUG] Selected 1 options
2025-08-21 12:56:30,961 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_CALL at 2025-08-07 08:45:00+00:00
[DEBUG] Selecting options: chain_size=2830, price=24626.65
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:56:30,963 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_CALL
[DEBUG] Selected 1 options
2025-08-21 12:56:30,963 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_CALL at 2025-08-07 09:55:00+00:00
[DEBUG] Selecting options: chain_size=2830, price=24626.65
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:56:30,964 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_CALL
[DEBUG] Selected 1 options
2025-08-21 12:56:30,964 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_CALL at 2025-08-07 09:55:00+00:00
[DEBUG] Selecting options: chain_size=2830, price=24626.65
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:56:30,966 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_CALL
[DEBUG] Selected 1 options
2025-08-21 12:56:30,966 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_CALL at 2025-08-07 09:55:00+00:00
[DEBUG] Processing 5 sample points with step_size=9175 for 45878 records on 2025-08-08
[DEBUG] Selecting options: chain_size=2951, price=24573.35
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:56:30,969 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_CALL
[DEBUG] Selected 1 options
2025-08-21 12:56:30,969 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_CALL at 2025-08-08 03:45:00+00:00
[DEBUG] Selecting options: chain_size=2951, price=24431.8
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:56:30,971 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_CALL
[DEBUG] Selected 1 options
2025-08-21 12:56:30,971 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_CALL at 2025-08-08 06:10:00+00:00
[DEBUG] Selecting options: chain_size=2951, price=24413.05
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:56:30,972 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_CALL
[DEBUG] Selected 1 options
2025-08-21 12:56:30,972 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_CALL at 2025-08-08 08:40:00+00:00
[DEBUG] Selecting options: chain_size=2951, price=24350.85
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:56:30,974 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_CALL
[DEBUG] Selected 1 options
2025-08-21 12:56:30,975 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_CALL at 2025-08-08 09:55:00+00:00
[DEBUG] Selecting options: chain_size=2951, price=24350.85
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:56:30,976 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_CALL
[DEBUG] Selected 1 options
2025-08-21 12:56:30,976 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_CALL at 2025-08-08 09:55:00+00:00
[DEBUG] Selecting options: chain_size=2951, price=24350.85
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:56:30,978 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_CALL
[DEBUG] Selected 1 options
2025-08-21 12:56:30,978 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_CALL at 2025-08-08 09:55:00+00:00
[DEBUG] Processing 5 sample points with step_size=9167 for 45835 records on 2025-08-11
[DEBUG] Selecting options: chain_size=2956, price=24401.5
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 11, 4, 35, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 643.45, 'high': 650.2, 'low': 643.45, 'close': 644.05, 'volume': 17, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 644.05, 'dte': 4}, {'timestamp': datetime.datetime(2025, 8, 11, 7, 45, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 713.45, 'high': 713.45, 'low': 711.6, 'close': 711.6, 'volume': 2, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 711.6, 'dte': 4}]
2025-08-21 12:56:30,981 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_CALL
[DEBUG] Selected 1 options
2025-08-21 12:56:30,981 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_CALL at 2025-08-11 03:45:00+00:00
[DEBUG] Selecting options: chain_size=2956, price=24414.15
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 11, 4, 35, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 643.45, 'high': 650.2, 'low': 643.45, 'close': 644.05, 'volume': 17, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 644.05, 'dte': 4}, {'timestamp': datetime.datetime(2025, 8, 11, 7, 45, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 713.45, 'high': 713.45, 'low': 711.6, 'close': 711.6, 'volume': 2, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 711.6, 'dte': 4}]
2025-08-21 12:56:30,983 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_CALL
[DEBUG] Selected 1 options
2025-08-21 12:56:30,983 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_CALL at 2025-08-11 06:15:00+00:00
[DEBUG] Selecting options: chain_size=2956, price=24537.25
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 11, 4, 35, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 643.45, 'high': 650.2, 'low': 643.45, 'close': 644.05, 'volume': 17, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 644.05, 'dte': 4}, {'timestamp': datetime.datetime(2025, 8, 11, 7, 45, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 713.45, 'high': 713.45, 'low': 711.6, 'close': 711.6, 'volume': 2, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 711.6, 'dte': 4}]
2025-08-21 12:56:30,984 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_CALL
[DEBUG] Selected 1 options
2025-08-21 12:56:30,985 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_CALL at 2025-08-11 08:45:00+00:00
[DEBUG] Selecting options: chain_size=2956, price=24562.15
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 11, 4, 35, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 643.45, 'high': 650.2, 'low': 643.45, 'close': 644.05, 'volume': 17, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 644.05, 'dte': 4}, {'timestamp': datetime.datetime(2025, 8, 11, 7, 45, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 713.45, 'high': 713.45, 'low': 711.6, 'close': 711.6, 'volume': 2, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 711.6, 'dte': 4}]
2025-08-21 12:56:30,987 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_CALL
[DEBUG] Selected 1 options
2025-08-21 12:56:30,987 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_CALL at 2025-08-11 09:55:00+00:00
[DEBUG] Selecting options: chain_size=2956, price=24562.15
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 11, 4, 35, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 643.45, 'high': 650.2, 'low': 643.45, 'close': 644.05, 'volume': 17, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 644.05, 'dte': 4}, {'timestamp': datetime.datetime(2025, 8, 11, 7, 45, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 713.45, 'high': 713.45, 'low': 711.6, 'close': 711.6, 'volume': 2, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 711.6, 'dte': 4}]
2025-08-21 12:56:30,989 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_CALL
[DEBUG] Selected 1 options
2025-08-21 12:56:30,989 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_CALL at 2025-08-11 09:55:00+00:00
2025-08-21 12:56:56,607 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Generated 17 completed trades for NIFTY_WEEKLY_MOMENTUM_CALL
2025-08-21 12:56:56,608 - agents.backtesting.strategy_definition_backtester - ERROR - [ERROR] Failed to calculate performance metrics: BacktestResults.__init__() got an unexpected keyword argument 'avg_return_per_trade'
2025-08-21 12:56:56,608 - agents.backtesting.strategy_definition_backtester - ERROR - [ERROR] Failed to backtest strategy NIFTY_WEEKLY_MOMENTUM_CALL: BacktestResults.__init__() got an unexpected keyword argument 'avg_return_per_trade'
2025-08-21 12:56:56,608 - agents.backtesting.strategy_definition_backtester - WARNING - [BACKTEST] Failed to backtest NIFTY_WEEKLY_MOMENTUM_CALL
2025-08-21 12:56:56,608 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Testing strategy definition: NIFTY_WEEKLY_MOMENTUM_PUT
2025-08-21 12:56:56,728 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Large dataset detected (2263464 records). Sampling for performance...
2025-08-21 12:56:56,749 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Sampled dataset: 226347 records
2025-08-21 12:56:56,750 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Data: 226347 historical records, 14474 option records
2025-08-21 12:56:56,751 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Date range: 2025-08-07 to 2025-08-13 (5 unique dates)
2025-08-21 12:56:56,752 - agents.backtesting.strategy_definition_backtester - INFO - [SIMULATE] Processing 3 unique dates for NIFTY_WEEKLY_MOMENTUM_PUT (limited for speed)
[DEBUG] Processing 5 sample points with step_size=8808 for 44043 records on 2025-08-07
[DEBUG] Selecting options: chain_size=2830, price=24519.45
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:56:56,754 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_PUT
[DEBUG] Selected 1 options
2025-08-21 12:56:56,754 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_PUT at 2025-08-07 03:45:00+00:00
[DEBUG] Selecting options: chain_size=2830, price=24422.2
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:56:56,756 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_PUT
[DEBUG] Selected 1 options
2025-08-21 12:56:56,756 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_PUT at 2025-08-07 06:15:00+00:00
[DEBUG] Selecting options: chain_size=2830, price=24467.3
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:56:56,758 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_PUT
[DEBUG] Selected 1 options
2025-08-21 12:56:56,758 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_PUT at 2025-08-07 08:45:00+00:00
[DEBUG] Selecting options: chain_size=2830, price=24626.65
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:56:56,760 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_PUT
[DEBUG] Selected 1 options
2025-08-21 12:56:56,760 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_PUT at 2025-08-07 09:55:00+00:00
[DEBUG] Selecting options: chain_size=2830, price=24626.65
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:56:56,762 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_PUT
[DEBUG] Selected 1 options
2025-08-21 12:56:56,762 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_PUT at 2025-08-07 09:55:00+00:00
[DEBUG] Selecting options: chain_size=2830, price=24626.65
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:56:56,764 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_PUT
[DEBUG] Selected 1 options
2025-08-21 12:56:56,764 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_PUT at 2025-08-07 09:55:00+00:00
[DEBUG] Processing 5 sample points with step_size=9175 for 45878 records on 2025-08-08
[DEBUG] Selecting options: chain_size=2951, price=24573.35
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:56:56,767 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_PUT
[DEBUG] Selected 1 options
2025-08-21 12:56:56,767 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_PUT at 2025-08-08 03:45:00+00:00
[DEBUG] Selecting options: chain_size=2951, price=24431.8
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:56:56,769 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_PUT
[DEBUG] Selected 1 options
2025-08-21 12:56:56,769 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_PUT at 2025-08-08 06:10:00+00:00
[DEBUG] Selecting options: chain_size=2951, price=24413.05
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:56:56,771 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_PUT
[DEBUG] Selected 1 options
2025-08-21 12:56:56,771 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_PUT at 2025-08-08 08:40:00+00:00
[DEBUG] Selecting options: chain_size=2951, price=24350.85
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:56:56,773 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_PUT
[DEBUG] Selected 1 options
2025-08-21 12:56:56,773 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_PUT at 2025-08-08 09:55:00+00:00
[DEBUG] Selecting options: chain_size=2951, price=24350.85
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:56:56,775 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_PUT
[DEBUG] Selected 1 options
2025-08-21 12:56:56,776 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_PUT at 2025-08-08 09:55:00+00:00
[DEBUG] Selecting options: chain_size=2951, price=24350.85
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:56:56,778 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_PUT
[DEBUG] Selected 1 options
2025-08-21 12:56:56,778 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_PUT at 2025-08-08 09:55:00+00:00
[DEBUG] Processing 5 sample points with step_size=9167 for 45835 records on 2025-08-11
[DEBUG] Selecting options: chain_size=2956, price=24401.5
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 11, 4, 35, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 643.45, 'high': 650.2, 'low': 643.45, 'close': 644.05, 'volume': 17, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 644.05, 'dte': 4}, {'timestamp': datetime.datetime(2025, 8, 11, 7, 45, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 713.45, 'high': 713.45, 'low': 711.6, 'close': 711.6, 'volume': 2, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 711.6, 'dte': 4}]
2025-08-21 12:56:56,780 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_PUT
[DEBUG] Selected 1 options
2025-08-21 12:56:56,780 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_PUT at 2025-08-11 03:45:00+00:00
[DEBUG] Selecting options: chain_size=2956, price=24414.15
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 11, 4, 35, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 643.45, 'high': 650.2, 'low': 643.45, 'close': 644.05, 'volume': 17, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 644.05, 'dte': 4}, {'timestamp': datetime.datetime(2025, 8, 11, 7, 45, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 713.45, 'high': 713.45, 'low': 711.6, 'close': 711.6, 'volume': 2, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 711.6, 'dte': 4}]
2025-08-21 12:56:56,783 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_PUT
[DEBUG] Selected 1 options
2025-08-21 12:56:56,783 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_PUT at 2025-08-11 06:15:00+00:00
[DEBUG] Selecting options: chain_size=2956, price=24537.25
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 11, 4, 35, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 643.45, 'high': 650.2, 'low': 643.45, 'close': 644.05, 'volume': 17, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 644.05, 'dte': 4}, {'timestamp': datetime.datetime(2025, 8, 11, 7, 45, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 713.45, 'high': 713.45, 'low': 711.6, 'close': 711.6, 'volume': 2, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 711.6, 'dte': 4}]
2025-08-21 12:56:56,785 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_PUT
[DEBUG] Selected 1 options
2025-08-21 12:56:56,785 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_PUT at 2025-08-11 08:45:00+00:00
[DEBUG] Selecting options: chain_size=2956, price=24562.15
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 11, 4, 35, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 643.45, 'high': 650.2, 'low': 643.45, 'close': 644.05, 'volume': 17, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 644.05, 'dte': 4}, {'timestamp': datetime.datetime(2025, 8, 11, 7, 45, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 713.45, 'high': 713.45, 'low': 711.6, 'close': 711.6, 'volume': 2, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 711.6, 'dte': 4}]
2025-08-21 12:56:56,787 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_PUT
[DEBUG] Selected 1 options
2025-08-21 12:56:56,787 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_PUT at 2025-08-11 09:55:00+00:00
[DEBUG] Selecting options: chain_size=2956, price=24562.15
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 11, 4, 35, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 643.45, 'high': 650.2, 'low': 643.45, 'close': 644.05, 'volume': 17, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 644.05, 'dte': 4}, {'timestamp': datetime.datetime(2025, 8, 11, 7, 45, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 713.45, 'high': 713.45, 'low': 711.6, 'close': 711.6, 'volume': 2, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 711.6, 'dte': 4}]
2025-08-21 12:56:56,789 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_MOMENTUM_PUT
[DEBUG] Selected 1 options
2025-08-21 12:56:56,789 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_MOMENTUM_PUT at 2025-08-11 09:55:00+00:00
2025-08-21 12:57:22,050 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Generated 17 completed trades for NIFTY_WEEKLY_MOMENTUM_PUT
2025-08-21 12:57:22,050 - agents.backtesting.strategy_definition_backtester - ERROR - [ERROR] Failed to calculate performance metrics: BacktestResults.__init__() got an unexpected keyword argument 'avg_return_per_trade'
2025-08-21 12:57:22,050 - agents.backtesting.strategy_definition_backtester - ERROR - [ERROR] Failed to backtest strategy NIFTY_WEEKLY_MOMENTUM_PUT: BacktestResults.__init__() got an unexpected keyword argument 'avg_return_per_trade'
2025-08-21 12:57:22,051 - agents.backtesting.strategy_definition_backtester - WARNING - [BACKTEST] Failed to backtest NIFTY_WEEKLY_MOMENTUM_PUT
2025-08-21 12:57:22,051 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Testing strategy definition: NIFTY_WEEKLY_BREAKOUT_CALL
2025-08-21 12:57:22,169 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Large dataset detected (2263464 records). Sampling for performance...
2025-08-21 12:57:22,189 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Sampled dataset: 226347 records
2025-08-21 12:57:22,191 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Data: 226347 historical records, 14474 option records
2025-08-21 12:57:22,192 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Date range: 2025-08-07 to 2025-08-13 (5 unique dates)
2025-08-21 12:57:22,193 - agents.backtesting.strategy_definition_backtester - INFO - [SIMULATE] Processing 3 unique dates for NIFTY_WEEKLY_BREAKOUT_CALL (limited for speed)
[DEBUG] Processing 5 sample points with step_size=8808 for 44043 records on 2025-08-07
[DEBUG] Selecting options: chain_size=2830, price=24519.45
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:57:22,196 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_CALL
[DEBUG] Selected 1 options
2025-08-21 12:57:22,196 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_CALL at 2025-08-07 03:45:00+00:00
[DEBUG] Selecting options: chain_size=2830, price=24422.2
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:57:22,198 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_CALL
[DEBUG] Selected 1 options
2025-08-21 12:57:22,198 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_CALL at 2025-08-07 06:15:00+00:00
[DEBUG] Selecting options: chain_size=2830, price=24467.3
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:57:22,200 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_CALL
[DEBUG] Selected 1 options
2025-08-21 12:57:22,200 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_CALL at 2025-08-07 08:45:00+00:00
[DEBUG] Selecting options: chain_size=2830, price=24626.65
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:57:22,202 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_CALL
[DEBUG] Selected 1 options
2025-08-21 12:57:22,202 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_CALL at 2025-08-07 09:55:00+00:00
[DEBUG] Selecting options: chain_size=2830, price=24626.65
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:57:22,204 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_CALL
[DEBUG] Selected 1 options
2025-08-21 12:57:22,204 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_CALL at 2025-08-07 09:55:00+00:00
[DEBUG] Selecting options: chain_size=2830, price=24626.65
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:57:22,207 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_CALL
[DEBUG] Selected 1 options
2025-08-21 12:57:22,207 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_CALL at 2025-08-07 09:55:00+00:00
[DEBUG] Processing 5 sample points with step_size=9175 for 45878 records on 2025-08-08
[DEBUG] Selecting options: chain_size=2951, price=24573.35
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:57:22,210 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_CALL
[DEBUG] Selected 1 options
2025-08-21 12:57:22,210 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_CALL at 2025-08-08 03:45:00+00:00
[DEBUG] Selecting options: chain_size=2951, price=24431.8
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:57:22,212 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_CALL
[DEBUG] Selected 1 options
2025-08-21 12:57:22,212 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_CALL at 2025-08-08 06:10:00+00:00
[DEBUG] Selecting options: chain_size=2951, price=24413.05
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:57:22,214 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_CALL
[DEBUG] Selected 1 options
2025-08-21 12:57:22,214 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_CALL at 2025-08-08 08:40:00+00:00
[DEBUG] Selecting options: chain_size=2951, price=24350.85
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:57:22,216 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_CALL
[DEBUG] Selected 1 options
2025-08-21 12:57:22,216 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_CALL at 2025-08-08 09:55:00+00:00
[DEBUG] Selecting options: chain_size=2951, price=24350.85
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:57:22,218 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_CALL
[DEBUG] Selected 1 options
2025-08-21 12:57:22,218 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_CALL at 2025-08-08 09:55:00+00:00
[DEBUG] Selecting options: chain_size=2951, price=24350.85
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:57:22,220 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_CALL
[DEBUG] Selected 1 options
2025-08-21 12:57:22,220 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_CALL at 2025-08-08 09:55:00+00:00
[DEBUG] Processing 5 sample points with step_size=9167 for 45835 records on 2025-08-11
[DEBUG] Selecting options: chain_size=2956, price=24401.5
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 11, 4, 35, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 643.45, 'high': 650.2, 'low': 643.45, 'close': 644.05, 'volume': 17, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 644.05, 'dte': 4}, {'timestamp': datetime.datetime(2025, 8, 11, 7, 45, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 713.45, 'high': 713.45, 'low': 711.6, 'close': 711.6, 'volume': 2, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 711.6, 'dte': 4}]
2025-08-21 12:57:22,223 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_CALL
[DEBUG] Selected 1 options
2025-08-21 12:57:22,223 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_CALL at 2025-08-11 03:45:00+00:00
[DEBUG] Selecting options: chain_size=2956, price=24414.15
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 11, 4, 35, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 643.45, 'high': 650.2, 'low': 643.45, 'close': 644.05, 'volume': 17, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 644.05, 'dte': 4}, {'timestamp': datetime.datetime(2025, 8, 11, 7, 45, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 713.45, 'high': 713.45, 'low': 711.6, 'close': 711.6, 'volume': 2, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 711.6, 'dte': 4}]
2025-08-21 12:57:22,225 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_CALL
[DEBUG] Selected 1 options
2025-08-21 12:57:22,225 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_CALL at 2025-08-11 06:15:00+00:00
[DEBUG] Selecting options: chain_size=2956, price=24537.25
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 11, 4, 35, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 643.45, 'high': 650.2, 'low': 643.45, 'close': 644.05, 'volume': 17, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 644.05, 'dte': 4}, {'timestamp': datetime.datetime(2025, 8, 11, 7, 45, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 713.45, 'high': 713.45, 'low': 711.6, 'close': 711.6, 'volume': 2, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 711.6, 'dte': 4}]
2025-08-21 12:57:22,227 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_CALL
[DEBUG] Selected 1 options
2025-08-21 12:57:22,228 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_CALL at 2025-08-11 08:45:00+00:00
[DEBUG] Selecting options: chain_size=2956, price=24562.15
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 11, 4, 35, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 643.45, 'high': 650.2, 'low': 643.45, 'close': 644.05, 'volume': 17, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 644.05, 'dte': 4}, {'timestamp': datetime.datetime(2025, 8, 11, 7, 45, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 713.45, 'high': 713.45, 'low': 711.6, 'close': 711.6, 'volume': 2, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 711.6, 'dte': 4}]
2025-08-21 12:57:22,229 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_CALL
[DEBUG] Selected 1 options
2025-08-21 12:57:22,230 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_CALL at 2025-08-11 09:55:00+00:00
[DEBUG] Selecting options: chain_size=2956, price=24562.15
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 11, 4, 35, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 643.45, 'high': 650.2, 'low': 643.45, 'close': 644.05, 'volume': 17, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 644.05, 'dte': 4}, {'timestamp': datetime.datetime(2025, 8, 11, 7, 45, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 713.45, 'high': 713.45, 'low': 711.6, 'close': 711.6, 'volume': 2, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 711.6, 'dte': 4}]
2025-08-21 12:57:22,232 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_CALL
[DEBUG] Selected 1 options
2025-08-21 12:57:22,232 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_CALL at 2025-08-11 09:55:00+00:00
2025-08-21 12:57:47,717 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Generated 17 completed trades for NIFTY_WEEKLY_BREAKOUT_CALL
2025-08-21 12:57:47,718 - agents.backtesting.strategy_definition_backtester - ERROR - [ERROR] Failed to calculate performance metrics: BacktestResults.__init__() got an unexpected keyword argument 'avg_return_per_trade'
2025-08-21 12:57:47,718 - agents.backtesting.strategy_definition_backtester - ERROR - [ERROR] Failed to backtest strategy NIFTY_WEEKLY_BREAKOUT_CALL: BacktestResults.__init__() got an unexpected keyword argument 'avg_return_per_trade'
2025-08-21 12:57:47,718 - agents.backtesting.strategy_definition_backtester - WARNING - [BACKTEST] Failed to backtest NIFTY_WEEKLY_BREAKOUT_CALL
2025-08-21 12:57:47,718 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Testing strategy definition: NIFTY_WEEKLY_BREAKOUT_PUT
2025-08-21 12:57:47,835 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Large dataset detected (2263464 records). Sampling for performance...
2025-08-21 12:57:47,856 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Sampled dataset: 226347 records
2025-08-21 12:57:47,858 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Data: 226347 historical records, 14474 option records
2025-08-21 12:57:47,858 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Date range: 2025-08-07 to 2025-08-13 (5 unique dates)
2025-08-21 12:57:47,859 - agents.backtesting.strategy_definition_backtester - INFO - [SIMULATE] Processing 3 unique dates for NIFTY_WEEKLY_BREAKOUT_PUT (limited for speed)
[DEBUG] Processing 5 sample points with step_size=8808 for 44043 records on 2025-08-07
[DEBUG] Selecting options: chain_size=2830, price=24519.45
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:57:47,862 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_PUT
[DEBUG] Selected 1 options
2025-08-21 12:57:47,862 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_PUT at 2025-08-07 03:45:00+00:00
[DEBUG] Selecting options: chain_size=2830, price=24422.2
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:57:47,864 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_PUT
[DEBUG] Selected 1 options
2025-08-21 12:57:47,864 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_PUT at 2025-08-07 06:15:00+00:00
[DEBUG] Selecting options: chain_size=2830, price=24467.3
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:57:47,866 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_PUT
[DEBUG] Selected 1 options
2025-08-21 12:57:47,866 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_PUT at 2025-08-07 08:45:00+00:00
[DEBUG] Selecting options: chain_size=2830, price=24626.65
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:57:47,868 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_PUT
[DEBUG] Selected 1 options
2025-08-21 12:57:47,868 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_PUT at 2025-08-07 09:55:00+00:00
[DEBUG] Selecting options: chain_size=2830, price=24626.65
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:57:47,870 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_PUT
[DEBUG] Selected 1 options
2025-08-21 12:57:47,870 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_PUT at 2025-08-07 09:55:00+00:00
[DEBUG] Selecting options: chain_size=2830, price=24626.65
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 7, 3, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 749.0, 'high': 755.4, 'low': 748.0, 'close': 755.4, 'volume': 27, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 755.4, 'dte': 1}, {'timestamp': datetime.datetime(2025, 8, 7, 3, 55, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 745.05, 'high': 749.8, 'low': 745.0, 'close': 745.0, 'volume': 7, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 7), 'open_interest': 0, 'premium': 745.0, 'dte': 1}]
2025-08-21 12:57:47,872 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_PUT
[DEBUG] Selected 1 options
2025-08-21 12:57:47,872 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_PUT at 2025-08-07 09:55:00+00:00
[DEBUG] Processing 5 sample points with step_size=9175 for 45878 records on 2025-08-08
[DEBUG] Selecting options: chain_size=2951, price=24573.35
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:57:47,875 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_PUT
[DEBUG] Selected 1 options
2025-08-21 12:57:47,875 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_PUT at 2025-08-08 03:45:00+00:00
[DEBUG] Selecting options: chain_size=2951, price=24431.8
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:57:47,876 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_PUT
[DEBUG] Selected 1 options
2025-08-21 12:57:47,877 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_PUT at 2025-08-08 06:10:00+00:00
[DEBUG] Selecting options: chain_size=2951, price=24413.05
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:57:47,878 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_PUT
[DEBUG] Selected 1 options
2025-08-21 12:57:47,878 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_PUT at 2025-08-08 08:40:00+00:00
[DEBUG] Selecting options: chain_size=2951, price=24350.85
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:57:47,880 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_PUT
[DEBUG] Selected 1 options
2025-08-21 12:57:47,880 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_PUT at 2025-08-08 09:55:00+00:00
[DEBUG] Selecting options: chain_size=2951, price=24350.85
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:57:47,882 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_PUT
[DEBUG] Selected 1 options
2025-08-21 12:57:47,883 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_PUT at 2025-08-08 09:55:00+00:00
[DEBUG] Selecting options: chain_size=2951, price=24350.85
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 8, 4, 50, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 695.4, 'high': 710.0, 'low': 690.0, 'close': 710.0, 'volume': 20, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 710.0, 'dte': 6}, {'timestamp': datetime.datetime(2025, 8, 8, 4, 15, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 739.75, 'high': 739.75, 'low': 738.7, 'close': 738.75, 'volume': 6, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 8), 'open_interest': 0, 'premium': 738.75, 'dte': 6}]
2025-08-21 12:57:47,885 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_PUT
[DEBUG] Selected 1 options
2025-08-21 12:57:47,885 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_PUT at 2025-08-08 09:55:00+00:00
[DEBUG] Processing 5 sample points with step_size=9167 for 45835 records on 2025-08-11
[DEBUG] Selecting options: chain_size=2956, price=24401.5
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 11, 4, 35, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 643.45, 'high': 650.2, 'low': 643.45, 'close': 644.05, 'volume': 17, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 644.05, 'dte': 4}, {'timestamp': datetime.datetime(2025, 8, 11, 7, 45, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 713.45, 'high': 713.45, 'low': 711.6, 'close': 711.6, 'volume': 2, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 711.6, 'dte': 4}]
2025-08-21 12:57:47,889 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_PUT
[DEBUG] Selected 1 options
2025-08-21 12:57:47,889 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_PUT at 2025-08-11 03:45:00+00:00
[DEBUG] Selecting options: chain_size=2956, price=24414.15
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 11, 4, 35, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 643.45, 'high': 650.2, 'low': 643.45, 'close': 644.05, 'volume': 17, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 644.05, 'dte': 4}, {'timestamp': datetime.datetime(2025, 8, 11, 7, 45, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 713.45, 'high': 713.45, 'low': 711.6, 'close': 711.6, 'volume': 2, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 711.6, 'dte': 4}]
2025-08-21 12:57:47,891 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_PUT
[DEBUG] Selected 1 options
2025-08-21 12:57:47,891 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_PUT at 2025-08-11 06:15:00+00:00
[DEBUG] Selecting options: chain_size=2956, price=24537.25
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 11, 4, 35, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 643.45, 'high': 650.2, 'low': 643.45, 'close': 644.05, 'volume': 17, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 644.05, 'dte': 4}, {'timestamp': datetime.datetime(2025, 8, 11, 7, 45, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 713.45, 'high': 713.45, 'low': 711.6, 'close': 711.6, 'volume': 2, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 711.6, 'dte': 4}]
2025-08-21 12:57:47,893 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_PUT
[DEBUG] Selected 1 options
2025-08-21 12:57:47,893 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_PUT at 2025-08-11 08:45:00+00:00
[DEBUG] Selecting options: chain_size=2956, price=24562.15
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 11, 4, 35, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 643.45, 'high': 650.2, 'low': 643.45, 'close': 644.05, 'volume': 17, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 644.05, 'dte': 4}, {'timestamp': datetime.datetime(2025, 8, 11, 7, 45, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 713.45, 'high': 713.45, 'low': 711.6, 'close': 711.6, 'volume': 2, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 711.6, 'dte': 4}]
2025-08-21 12:57:47,895 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_PUT
[DEBUG] Selected 1 options
2025-08-21 12:57:47,895 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_PUT at 2025-08-11 09:55:00+00:00
[DEBUG] Selecting options: chain_size=2956, price=24562.15
[DEBUG] Chain columns: ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'underlying', 'strike_price', 'option_type', 'symbol', 'timeframe', 'instrument_type', 'date', 'open_interest', 'premium', 'dte']
[DEBUG] Sample chain data: [{'timestamp': datetime.datetime(2025, 8, 11, 4, 35, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 643.45, 'high': 650.2, 'low': 643.45, 'close': 644.05, 'volume': 17, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 644.05, 'dte': 4}, {'timestamp': datetime.datetime(2025, 8, 11, 7, 45, tzinfo=zoneinfo.ZoneInfo(key='UTC')), 'open': 713.45, 'high': 713.45, 'low': 711.6, 'close': 711.6, 'volume': 2, 'underlying': 'NIFTY', 'strike_price': 23800.0, 'option_type': 'CE', 'symbol': 'NIFTY_23800_CE', 'timeframe': '5min', 'instrument_type': 'OPTION', 'date': datetime.date(2025, 8, 11), 'open_interest': 0, 'premium': 711.6, 'dte': 4}]
2025-08-21 12:57:47,897 - agents.signal_generation.option_selector - INFO - Selected 1 options for strategy NIFTY_WEEKLY_BREAKOUT_PUT
[DEBUG] Selected 1 options
2025-08-21 12:57:47,897 - agents.backtesting.strategy_definition_backtester - INFO - [TRADE] Entry: NIFTY_WEEKLY_BREAKOUT_PUT at 2025-08-11 09:55:00+00:00
2025-08-21 12:58:13,232 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Generated 17 completed trades for NIFTY_WEEKLY_BREAKOUT_PUT
2025-08-21 12:58:13,233 - agents.backtesting.strategy_definition_backtester - ERROR - [ERROR] Failed to calculate performance metrics: BacktestResults.__init__() got an unexpected keyword argument 'avg_return_per_trade'
2025-08-21 12:58:13,233 - agents.backtesting.strategy_definition_backtester - ERROR - [ERROR] Failed to backtest strategy NIFTY_WEEKLY_BREAKOUT_PUT: BacktestResults.__init__() got an unexpected keyword argument 'avg_return_per_trade'
2025-08-21 12:58:13,233 - agents.backtesting.strategy_definition_backtester - WARNING - [BACKTEST] Failed to backtest NIFTY_WEEKLY_BREAKOUT_PUT
2025-08-21 12:58:13,233 - agents.backtesting.strategy_definition_backtester - INFO - [BACKTEST] Completed backtesting for 0 strategy definitions
2025-08-21 12:58:13,233 - agents.backtesting_agent - WARNING - No results from strategy definition backtesting
2025-08-21 12:58:13,234 - agents.backtesting_agent - WARNING - Strategy definition backtesting failed, falling back to traditional strategies
2025-08-21 12:58:13,234 - agents.backtesting.data_loader - INFO - Loading latest generated strategies...
2025-08-21 12:58:13,234 - agents.backtesting.data_loader - WARNING - No strategy files found
2025-08-21 12:58:13,234 - agents.backtesting_agent - WARNING - No strategies found for backtesting
2025-08-21 12:58:13,234 - __main__ - ERROR - [ERROR] backtesting agent failed to start
[ERROR] backtesting agent failed
2025-08-21 12:58:13,234 - __main__ - INFO - [CLEANUP] Shutting down all agents...
2025-08-21 12:58:13,234 - agents.backtesting_agent - INFO - Cleaning up Options Backtesting Agent...
2025-08-21 12:58:13,234 - __main__ - INFO - [CLEANUP] backtesting agent cleaned up
2025-08-21 12:58:13,234 - __main__ - INFO - [CLEANUP] All agents cleaned up successfull
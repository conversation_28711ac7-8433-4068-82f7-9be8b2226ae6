#!/usr/bin/env python3
"""
Test script for optimized backtesting with minimal warnings
"""
import asyncio
import logging
import sys
from pathlib import Path

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent))

from agents.backtesting_agent import OptionsBacktestingAgent

# Configure logging to reduce verbosity
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Reduce logging for specific modules
logging.getLogger('agents.signal_generation.option_selector').setLevel(logging.ERROR)
logging.getLogger('agents.backtesting.strategy_definition_backtester').setLevel(logging.WARNING)

async def test_optimized_backtest():
    """Test the optimized backtesting with reduced warnings"""
    try:
        print("Starting optimized backtesting test...")
        
        agent = OptionsBacktestingAgent()
        
        # Initialize agent
        success = await agent.initialize()
        if not success:
            print("Failed to initialize agent")
            return False
        
        print(f"Loaded {len(agent.strategy_definitions)} optimized strategies")
        
        # Run backtest with short date range for testing
        success = await agent.start(
            start_date="2025-08-07",
            end_date="2025-08-13",
            use_strategy_definitions=True
        )
        
        if success:
            print("Optimized backtesting completed successfully!")
            print(f"Results: {len(agent.backtest_results)} strategies backtested")
            
            # Print summary
            for strategy_id, results in agent.backtest_results.items():
                print(f"  {strategy_id}: {results.total_trades} trades, "
                      f"{results.win_rate:.1%} win rate, "
                      f"{results.total_return:.1%} return")
        else:
            print("Backtesting failed")
            
        return success
        
    except Exception as e:
        print(f"Test failed: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_optimized_backtest())
    print(f"Test {'PASSED' if result else 'FAILED'}")
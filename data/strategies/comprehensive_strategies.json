[{"strategy_id": "NIFTY_WEEKLY_MOMENTUM_CALL", "strategy_type": "long_call", "underlying": "NIFTY", "name": "NIFTY Weekly Momentum Call", "description": "Simple momentum-based call buying for weekly NIFTY options", "selection_criteria": {"call_leg": {"moneyness": "ATM", "dte_range": [0, 7], "delta_range": [0.1, 0.9], "strike_range": ["5%_ITM", "10%_OTM"], "volume_threshold": 1, "open_interest_threshold": 0, "max_bid_ask_spread": null}}, "entry_conditions": [{"indicator": "close", "operator": ">", "value": "sma_10"}, {"indicator": "returns_pct", "operator": ">", "value": 0.1}], "exit_conditions": [{"indicator": "profit_pct", "operator": ">=", "value": 30}, {"indicator": "loss_pct", "operator": "<=", "value": -50}, {"indicator": "dte", "operator": "<=", "value": 1}], "risk_management": {"stop_loss": 0.5, "take_profit": 0.3, "position_size": 0.02, "max_positions": 3}, "market_outlook": "bullish", "volatility_outlook": "neutral", "timeframe": "5min", "created_at": "2025-08-21T12:00:00.000000", "tags": ["momentum", "weekly", "simple", "optimized"]}, {"strategy_id": "NIFTY_WEEKLY_MOMENTUM_PUT", "strategy_type": "long_put", "underlying": "NIFTY", "name": "NIFTY Weekly Momentum Put", "description": "Simple momentum-based put buying for weekly NIFTY options", "selection_criteria": {"put_leg": {"moneyness": "ATM", "dte_range": [0, 7], "delta_range": [-0.9, -0.1], "strike_range": ["5%_ITM", "10%_OTM"], "volume_threshold": 1, "open_interest_threshold": 0, "max_bid_ask_spread": null}}, "entry_conditions": [{"indicator": "close", "operator": "<", "value": "sma_10"}, {"indicator": "returns_pct", "operator": "<", "value": -0.1}], "exit_conditions": [{"indicator": "profit_pct", "operator": ">=", "value": 30}, {"indicator": "loss_pct", "operator": "<=", "value": -50}, {"indicator": "dte", "operator": "<=", "value": 1}], "risk_management": {"stop_loss": 0.5, "take_profit": 0.3, "position_size": 0.02, "max_positions": 3}, "market_outlook": "bearish", "volatility_outlook": "neutral", "timeframe": "5min", "created_at": "2025-08-21T12:00:00.000000", "tags": ["momentum", "weekly", "simple", "optimized"]}, {"strategy_id": "BANKNIFTY_WEEKLY_MOMENTUM_CALL", "strategy_type": "long_call", "underlying": "BANKNIFTY", "name": "BANKNIFTY Weekly Momentum Call", "description": "Momentum-based call buying for weekly BANKNIFTY options", "selection_criteria": {"call_leg": {"moneyness": "ATM", "dte_range": [0, 30], "delta_range": [0.1, 0.9], "strike_range": ["5%_ITM", "10%_OTM"], "volume_threshold": 1, "open_interest_threshold": 0, "max_bid_ask_spread": null}}, "entry_conditions": [{"indicator": "close", "operator": ">", "value": "sma_10"}, {"indicator": "returns_pct", "operator": ">", "value": 0.15}], "exit_conditions": [{"indicator": "profit_pct", "operator": ">=", "value": 25}, {"indicator": "loss_pct", "operator": "<=", "value": -40}, {"indicator": "dte", "operator": "<=", "value": 2}], "risk_management": {"stop_loss": 0.4, "take_profit": 0.25, "position_size": 0.015, "max_positions": 2}, "market_outlook": "bullish", "volatility_outlook": "neutral", "timeframe": "5min", "created_at": "2025-08-21T12:00:00.000000", "tags": ["momentum", "weekly", "banknifty", "optimized"]}, {"strategy_id": "BANKNIFTY_WEEKLY_MOMENTUM_PUT", "strategy_type": "long_put", "underlying": "BANKNIFTY", "name": "BANKNIFTY Weekly Momentum Put", "description": "Momentum-based put buying for weekly BANKNIFTY options", "selection_criteria": {"put_leg": {"moneyness": "ATM", "dte_range": [0, 30], "delta_range": [-0.9, -0.1], "strike_range": ["5%_ITM", "10%_OTM"], "volume_threshold": 1, "open_interest_threshold": 0, "max_bid_ask_spread": null}}, "entry_conditions": [{"indicator": "close", "operator": "<", "value": "sma_10"}, {"indicator": "returns_pct", "operator": "<", "value": -0.15}], "exit_conditions": [{"indicator": "profit_pct", "operator": ">=", "value": 25}, {"indicator": "loss_pct", "operator": "<=", "value": -40}, {"indicator": "dte", "operator": "<=", "value": 2}], "risk_management": {"stop_loss": 0.4, "take_profit": 0.25, "position_size": 0.015, "max_positions": 2}, "market_outlook": "bearish", "volatility_outlook": "neutral", "timeframe": "5min", "created_at": "2025-08-21T12:00:00.000000", "tags": ["momentum", "weekly", "banknifty", "optimized"]}, {"strategy_id": "NIFTY_BREAKOUT_CALL", "strategy_type": "long_call", "underlying": "NIFTY", "name": "NIFTY Breakout Call", "description": "Breakout-based call buying for NIFTY options", "selection_criteria": {"call_leg": {"moneyness": "ATM", "dte_range": [0, 7], "delta_range": [0.1, 0.9], "strike_range": ["5%_ITM", "10%_OTM"], "volume_threshold": 1, "open_interest_threshold": 0, "max_bid_ask_spread": null}}, "entry_conditions": [{"indicator": "close", "operator": ">", "value": "sma_20"}, {"indicator": "volume", "operator": ">", "value": 100}], "exit_conditions": [{"indicator": "profit_pct", "operator": ">=", "value": 40}, {"indicator": "loss_pct", "operator": "<=", "value": -40}, {"indicator": "dte", "operator": "<=", "value": 1}], "risk_management": {"stop_loss": 0.4, "take_profit": 0.4, "position_size": 0.03, "max_positions": 2}, "market_outlook": "bullish", "volatility_outlook": "neutral", "timeframe": "15min", "created_at": "2025-08-21T12:00:00.000000", "tags": ["breakout", "weekly", "simple", "optimized"]}, {"strategy_id": "NIFTY_BREAKOUT_PUT", "strategy_type": "long_put", "underlying": "NIFTY", "name": "NIFTY Breakout Put", "description": "Breakout-based put buying for NIFTY options", "selection_criteria": {"put_leg": {"moneyness": "ATM", "dte_range": [0, 7], "delta_range": [-0.9, -0.1], "strike_range": ["5%_ITM", "10%_OTM"], "volume_threshold": 1, "open_interest_threshold": 0, "max_bid_ask_spread": null}}, "entry_conditions": [{"indicator": "close", "operator": "<", "value": "sma_20"}, {"indicator": "volume", "operator": ">", "value": 100}], "exit_conditions": [{"indicator": "profit_pct", "operator": ">=", "value": 40}, {"indicator": "loss_pct", "operator": "<=", "value": -40}, {"indicator": "dte", "operator": "<=", "value": 1}], "risk_management": {"stop_loss": 0.4, "take_profit": 0.4, "position_size": 0.03, "max_positions": 2}, "market_outlook": "bearish", "volatility_outlook": "neutral", "timeframe": "15min", "created_at": "2025-08-21T12:00:00.000000", "tags": ["breakout", "weekly", "simple", "optimized"]}, {"strategy_id": "BANKNIFTY_BREAKOUT_CALL", "strategy_type": "long_call", "underlying": "BANKNIFTY", "name": "BANKNIFTY Breakout Call", "description": "Breakout-based call buying for BANKNIFTY options", "selection_criteria": {"call_leg": {"moneyness": "ATM", "dte_range": [0, 30], "delta_range": [0.1, 0.9], "strike_range": ["5%_ITM", "10%_OTM"], "volume_threshold": 1, "open_interest_threshold": 0, "max_bid_ask_spread": null}}, "entry_conditions": [{"indicator": "close", "operator": ">", "value": "sma_20"}, {"indicator": "volume", "operator": ">", "value": 100}], "exit_conditions": [{"indicator": "profit_pct", "operator": ">=", "value": 35}, {"indicator": "loss_pct", "operator": "<=", "value": -35}, {"indicator": "dte", "operator": "<=", "value": 2}], "risk_management": {"stop_loss": 0.35, "take_profit": 0.35, "position_size": 0.02, "max_positions": 2}, "market_outlook": "bullish", "volatility_outlook": "neutral", "timeframe": "15min", "created_at": "2025-08-21T12:00:00.000000", "tags": ["breakout", "weekly", "banknifty", "optimized"]}, {"strategy_id": "BANKNIFTY_BREAKOUT_PUT", "strategy_type": "long_put", "underlying": "BANKNIFTY", "name": "BANKNIFTY Breakout Put", "description": "Breakout-based put buying for BANKNIFTY options", "selection_criteria": {"put_leg": {"moneyness": "ATM", "dte_range": [0, 30], "delta_range": [-0.9, -0.1], "strike_range": ["5%_ITM", "10%_OTM"], "volume_threshold": 1, "open_interest_threshold": 0, "max_bid_ask_spread": null}}, "entry_conditions": [{"indicator": "close", "operator": "<", "value": "sma_20"}, {"indicator": "volume", "operator": ">", "value": 100}], "exit_conditions": [{"indicator": "profit_pct", "operator": ">=", "value": 35}, {"indicator": "loss_pct", "operator": "<=", "value": -35}, {"indicator": "dte", "operator": "<=", "value": 2}], "risk_management": {"stop_loss": 0.35, "take_profit": 0.35, "position_size": 0.02, "max_positions": 2}, "market_outlook": "bearish", "volatility_outlook": "neutral", "timeframe": "15min", "created_at": "2025-08-21T12:00:00.000000", "tags": ["breakout", "weekly", "banknifty", "optimized"]}, {"strategy_id": "NIFTY_SCALPING_CALL", "strategy_type": "long_call", "underlying": "NIFTY", "name": "NIFTY Scalping Call", "description": "Quick scalping strategy for NIFTY calls using 5-minute momentum", "selection_criteria": {"call_leg": {"moneyness": "ATM", "dte_range": [0, 7], "delta_range": [0.3, 0.7], "strike_range": ["2%_ITM", "5%_OTM"], "volume_threshold": 10, "open_interest_threshold": 0, "max_bid_ask_spread": null}}, "entry_conditions": [{"indicator": "close", "operator": ">", "value": "sma_5"}, {"indicator": "volume", "operator": ">", "value": 50}], "exit_conditions": [{"indicator": "profit_pct", "operator": ">=", "value": 15}, {"indicator": "loss_pct", "operator": "<=", "value": -25}, {"indicator": "time_minutes", "operator": ">=", "value": 30}], "risk_management": {"stop_loss": 0.25, "take_profit": 0.15, "position_size": 0.03, "max_positions": 5}, "market_outlook": "bullish", "volatility_outlook": "high", "timeframe": "5min", "created_at": "2025-08-21T14:00:00.000000", "tags": ["scalping", "short-term", "momentum", "nifty"]}, {"strategy_id": "NIFTY_SCALPING_PUT", "strategy_type": "long_put", "underlying": "NIFTY", "name": "NIFTY Scalping Put", "description": "Quick scalping strategy for NIFTY puts using 5-minute momentum", "selection_criteria": {"put_leg": {"moneyness": "ATM", "dte_range": [0, 7], "delta_range": [-0.7, -0.3], "strike_range": ["2%_ITM", "5%_OTM"], "volume_threshold": 10, "open_interest_threshold": 0, "max_bid_ask_spread": null}}, "entry_conditions": [{"indicator": "close", "operator": "<", "value": "sma_5"}, {"indicator": "volume", "operator": ">", "value": 50}], "exit_conditions": [{"indicator": "profit_pct", "operator": ">=", "value": 15}, {"indicator": "loss_pct", "operator": "<=", "value": -25}, {"indicator": "time_minutes", "operator": ">=", "value": 30}], "risk_management": {"stop_loss": 0.25, "take_profit": 0.15, "position_size": 0.03, "max_positions": 5}, "market_outlook": "bearish", "volatility_outlook": "high", "timeframe": "5min", "created_at": "2025-08-21T14:00:00.000000", "tags": ["scalping", "short-term", "momentum", "nifty"]}, {"strategy_id": "BANKNIFTY_SCALPING_CALL", "strategy_type": "long_call", "underlying": "BANKNIFTY", "name": "BANKNIFTY Scalping Call", "description": "Quick scalping strategy for BANKNIFTY calls using 5-minute momentum", "selection_criteria": {"call_leg": {"moneyness": "ATM", "dte_range": [0, 30], "delta_range": [0.3, 0.7], "strike_range": ["2%_ITM", "5%_OTM"], "volume_threshold": 10, "open_interest_threshold": 0, "max_bid_ask_spread": null}}, "entry_conditions": [{"indicator": "close", "operator": ">", "value": "sma_5"}, {"indicator": "volume", "operator": ">", "value": 50}], "exit_conditions": [{"indicator": "profit_pct", "operator": ">=", "value": 20}, {"indicator": "loss_pct", "operator": "<=", "value": -30}, {"indicator": "time_minutes", "operator": ">=", "value": 45}], "risk_management": {"stop_loss": 0.3, "take_profit": 0.2, "position_size": 0.025, "max_positions": 3}, "market_outlook": "bullish", "volatility_outlook": "high", "timeframe": "5min", "created_at": "2025-08-21T14:00:00.000000", "tags": ["scalping", "short-term", "momentum", "banknifty"]}, {"strategy_id": "BANKNIFTY_SCALPING_PUT", "strategy_type": "long_put", "underlying": "BANKNIFTY", "name": "BANKNIFTY Scalping Put", "description": "Quick scalping strategy for BANKNIFTY puts using 5-minute momentum", "selection_criteria": {"put_leg": {"moneyness": "ATM", "dte_range": [0, 30], "delta_range": [-0.7, -0.3], "strike_range": ["2%_ITM", "5%_OTM"], "volume_threshold": 10, "open_interest_threshold": 0, "max_bid_ask_spread": null}}, "entry_conditions": [{"indicator": "close", "operator": "<", "value": "sma_5"}, {"indicator": "volume", "operator": ">", "value": 50}], "exit_conditions": [{"indicator": "profit_pct", "operator": ">=", "value": 20}, {"indicator": "loss_pct", "operator": "<=", "value": -30}, {"indicator": "time_minutes", "operator": ">=", "value": 45}], "risk_management": {"stop_loss": 0.3, "take_profit": 0.2, "position_size": 0.025, "max_positions": 3}, "market_outlook": "bearish", "volatility_outlook": "high", "timeframe": "5min", "created_at": "2025-08-21T14:00:00.000000", "tags": ["scalping", "short-term", "momentum", "banknifty"]}]
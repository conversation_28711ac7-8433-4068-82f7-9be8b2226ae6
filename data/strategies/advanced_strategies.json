[{"strategy_id": "NIFTY_SCALPING_CALL", "strategy_type": "long_call", "underlying": "NIFTY", "name": "NIFTY Scalping Call", "description": "Quick scalping strategy for NIFTY calls using 5-minute momentum", "selection_criteria": {"call_leg": {"moneyness": "ATM", "dte_range": [0, 7], "delta_range": [0.3, 0.7], "strike_range": ["2%_ITM", "5%_OTM"], "volume_threshold": 10, "open_interest_threshold": 0, "max_bid_ask_spread": null}}, "entry_conditions": [{"indicator": "close", "operator": ">", "value": "sma_5"}, {"indicator": "volume", "operator": ">", "value": "sma_volume_10"}], "exit_conditions": [{"indicator": "profit_pct", "operator": ">=", "value": 15}, {"indicator": "loss_pct", "operator": "<=", "value": -25}, {"indicator": "time_minutes", "operator": ">=", "value": 30}], "risk_management": {"stop_loss": 0.25, "take_profit": 0.15, "position_size": 0.03, "max_positions": 5}, "market_outlook": "bullish", "volatility_outlook": "high", "timeframe": "5min", "created_at": "2025-08-21T14:00:00.000000", "tags": ["scalping", "short-term", "momentum", "nifty"]}, {"strategy_id": "NIFTY_SCALPING_PUT", "strategy_type": "long_put", "underlying": "NIFTY", "name": "NIFTY Scalping Put", "description": "Quick scalping strategy for NIFTY puts using 5-minute momentum", "selection_criteria": {"put_leg": {"moneyness": "ATM", "dte_range": [0, 7], "delta_range": [-0.7, -0.3], "strike_range": ["2%_ITM", "5%_OTM"], "volume_threshold": 10, "open_interest_threshold": 0, "max_bid_ask_spread": null}}, "entry_conditions": [{"indicator": "close", "operator": "<", "value": "sma_5"}, {"indicator": "volume", "operator": ">", "value": "sma_volume_10"}], "exit_conditions": [{"indicator": "profit_pct", "operator": ">=", "value": 15}, {"indicator": "loss_pct", "operator": "<=", "value": -25}, {"indicator": "time_minutes", "operator": ">=", "value": 30}], "risk_management": {"stop_loss": 0.25, "take_profit": 0.15, "position_size": 0.03, "max_positions": 5}, "market_outlook": "bearish", "volatility_outlook": "high", "timeframe": "5min", "created_at": "2025-08-21T14:00:00.000000", "tags": ["scalping", "short-term", "momentum", "nifty"]}, {"strategy_id": "BANKNIFTY_SCALPING_CALL", "strategy_type": "long_call", "underlying": "BANKNIFTY", "name": "BANKNIFTY Scalping Call", "description": "Quick scalping strategy for BANKNIFTY calls using 5-minute momentum", "selection_criteria": {"call_leg": {"moneyness": "ATM", "dte_range": [0, 30], "delta_range": [0.3, 0.7], "strike_range": ["2%_ITM", "5%_OTM"], "volume_threshold": 10, "open_interest_threshold": 0, "max_bid_ask_spread": null}}, "entry_conditions": [{"indicator": "close", "operator": ">", "value": "sma_5"}, {"indicator": "volume", "operator": ">", "value": "sma_volume_10"}], "exit_conditions": [{"indicator": "profit_pct", "operator": ">=", "value": 20}, {"indicator": "loss_pct", "operator": "<=", "value": -30}, {"indicator": "time_minutes", "operator": ">=", "value": 45}], "risk_management": {"stop_loss": 0.3, "take_profit": 0.2, "position_size": 0.025, "max_positions": 3}, "market_outlook": "bullish", "volatility_outlook": "high", "timeframe": "5min", "created_at": "2025-08-21T14:00:00.000000", "tags": ["scalping", "short-term", "momentum", "banknifty"]}, {"strategy_id": "BANKNIFTY_SCALPING_PUT", "strategy_type": "long_put", "underlying": "BANKNIFTY", "name": "BANKNIFTY Scalping Put", "description": "Quick scalping strategy for BANKNIFTY puts using 5-minute momentum", "selection_criteria": {"put_leg": {"moneyness": "ATM", "dte_range": [0, 30], "delta_range": [-0.7, -0.3], "strike_range": ["2%_ITM", "5%_OTM"], "volume_threshold": 10, "open_interest_threshold": 0, "max_bid_ask_spread": null}}, "entry_conditions": [{"indicator": "close", "operator": "<", "value": "sma_5"}, {"indicator": "volume", "operator": ">", "value": "sma_volume_10"}], "exit_conditions": [{"indicator": "profit_pct", "operator": ">=", "value": 20}, {"indicator": "loss_pct", "operator": "<=", "value": -30}, {"indicator": "time_minutes", "operator": ">=", "value": 45}], "risk_management": {"stop_loss": 0.3, "take_profit": 0.2, "position_size": 0.025, "max_positions": 3}, "market_outlook": "bearish", "volatility_outlook": "high", "timeframe": "5min", "created_at": "2025-08-21T14:00:00.000000", "tags": ["scalping", "short-term", "momentum", "banknifty"]}, {"strategy_id": "NIFTY_MEAN_REVERSION_CALL", "strategy_type": "long_call", "underlying": "NIFTY", "name": "NIFTY Mean Reversion Call", "description": "Mean reversion strategy buying calls when NIFTY is oversold", "selection_criteria": {"call_leg": {"moneyness": "ATM", "dte_range": [1, 7], "delta_range": [0.2, 0.8], "strike_range": ["3%_ITM", "8%_OTM"], "volume_threshold": 5, "open_interest_threshold": 0, "max_bid_ask_spread": null}}, "entry_conditions": [{"indicator": "rsi_14", "operator": "<", "value": 35}, {"indicator": "close", "operator": "<", "value": "sma_20"}], "exit_conditions": [{"indicator": "profit_pct", "operator": ">=", "value": 25}, {"indicator": "loss_pct", "operator": "<=", "value": -40}, {"indicator": "rsi_14", "operator": ">", "value": 65}], "risk_management": {"stop_loss": 0.4, "take_profit": 0.25, "position_size": 0.025, "max_positions": 3}, "market_outlook": "neutral", "volatility_outlook": "neutral", "timeframe": "15min", "created_at": "2025-08-21T14:00:00.000000", "tags": ["mean-reversion", "rsi", "oversold", "nifty"]}, {"strategy_id": "NIFTY_MEAN_REVERSION_PUT", "strategy_type": "long_put", "underlying": "NIFTY", "name": "NIFTY Mean Reversion Put", "description": "Mean reversion strategy buying puts when NIFTY is overbought", "selection_criteria": {"put_leg": {"moneyness": "ATM", "dte_range": [1, 7], "delta_range": [-0.8, -0.2], "strike_range": ["3%_ITM", "8%_OTM"], "volume_threshold": 5, "open_interest_threshold": 0, "max_bid_ask_spread": null}}, "entry_conditions": [{"indicator": "rsi_14", "operator": ">", "value": 65}, {"indicator": "close", "operator": ">", "value": "sma_20"}], "exit_conditions": [{"indicator": "profit_pct", "operator": ">=", "value": 25}, {"indicator": "loss_pct", "operator": "<=", "value": -40}, {"indicator": "rsi_14", "operator": "<", "value": 35}], "risk_management": {"stop_loss": 0.4, "take_profit": 0.25, "position_size": 0.025, "max_positions": 3}, "market_outlook": "neutral", "volatility_outlook": "neutral", "timeframe": "15min", "created_at": "2025-08-21T14:00:00.000000", "tags": ["mean-reversion", "rsi", "overbought", "nifty"]}]